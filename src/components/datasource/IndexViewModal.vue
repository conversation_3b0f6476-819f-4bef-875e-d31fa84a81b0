<template>
  <a-modal
    :open="visible"
    title="数据库索引结构"
    width="800px"
    :footer="null"
    @cancel="handleClose"
    @ok="handleClose"
  >
    <div class="index-view-modal">
      <!-- 表信息头部 -->
      <div class="mb-4 p-3 bg-gray-50 rounded-lg">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-medium text-gray-900">{{ tableName }}</h3>
            <p class="text-sm text-gray-500">数据库索引结构</p>
          </div>

        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="py-10 text-center">
        <div class="animate-pulse flex justify-center">
          <div class="h-8 w-8 bg-indigo-200 rounded-full"></div>
        </div>
        <p class="mt-2 text-sm text-gray-500">正在加载索引信息...</p>
      </div>

      <!-- 无索引状态 -->
      <div v-else-if="!indexes.length" class="py-10 text-center bg-gray-50 rounded-lg">
        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无索引信息</h3>
        <p class="mt-1 text-sm text-gray-500">该表目前没有创建任何索引</p>
      </div>

      <!-- 索引列表 -->
      <div v-else class="space-y-4">
        <div v-for="index in indexes" :key="index.indexName" class="border border-gray-200 rounded-lg overflow-hidden">
          <!-- 索引头部 -->
          <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div>
                  <span class="font-medium text-gray-900">{{ index.indexName }}</span>
                  <div v-if="index.description" class="text-xs text-gray-500 mt-1">
                    {{ index.description }}
                  </div>
                </div>
                <div class="flex space-x-2">
                  <!-- 索引类型标签 -->
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        :class="getIndexTypeClass(index.indexType)">
                    {{ getIndexTypeLabel(index.indexType) }}
                  </span>
                  <!-- 主键标签 -->
                  <span v-if="index.isPrimary" 
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    主键
                  </span>
                  <!-- 唯一性标签 -->
                  <span v-else-if="index.isUnique" 
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    唯一
                  </span>
                </div>
              </div>
              <div class="text-sm text-gray-500">
                {{ index.columnCount }} 列
              </div>
            </div>
          </div>
          
          <!-- 索引列信息 -->
          <div class="bg-white p-4">
            <div class="text-sm font-medium text-gray-700 mb-2">索引列:</div>
            <div v-if="index.columns && index.columns.length > 0" class="space-y-2">
              <div v-for="column in getSortedColumns(index.columns)" :key="column.position"
                   class="flex items-center space-x-3 p-2 bg-gray-50 rounded">
                <span class="text-sm font-medium text-gray-900">{{ column.position }}</span>
                <span class="text-sm text-gray-700">{{ column.columnName }}</span>
                <span v-if="column.isPrimary" class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded">
                  主列
                </span>
                <span class="text-xs text-gray-500">
                  {{ column.order }}
                </span>
              </div>
            </div>
            <div v-else class="text-sm text-gray-500 italic">
              列信息暂未获取，列数: {{ index.columnCount }}
            </div>
          </div>
        </div>
      </div>


    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { IndexMetadata, IndexColumn } from '@/types/metadata'
import { message } from 'ant-design-vue'
import { dataSourceService } from '@/services/datasource'

// 组件属性
const props = defineProps<{
  visible: boolean
  tableId?: string
  tableName: string
  dataSourceId?: string
  schema?: string
}>()

// 组件事件
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
}>()

// 组件状态
const isLoading = ref(false)
const indexes = ref<IndexMetadata[]>([])



// 监听visible变化，当打开时加载索引数据
watch(() => props.visible, (newVisible) => {
  console.log(`[IndexViewModal] visible 变化: ${newVisible}, tableId: ${props.tableId}`);
  if (newVisible && props.tableId) {
    console.log('[IndexViewModal] 开始加载索引数据');
    loadIndexes();
  } else if (!newVisible) {
    console.log('[IndexViewModal] 关闭模态框，清空索引数据');
    indexes.value = [];
  }
})

// 获取索引类型样式类
const getIndexTypeClass = (type: string) => {
  switch (type) {
    case 'BTREE':
      return 'bg-blue-100 text-blue-800'
    case 'HASH':
      return 'bg-green-100 text-green-800'
    case 'FULLTEXT':
      return 'bg-purple-100 text-purple-800'
    case 'SPATIAL':
      return 'bg-orange-100 text-orange-800'
    case 'UNIQUE':
      return 'bg-indigo-100 text-indigo-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 获取索引类型标签
const getIndexTypeLabel = (type: string) => {
  switch (type) {
    case 'BTREE':
      return 'BTREE'
    case 'HASH':
      return 'HASH'
    case 'FULLTEXT':
      return '全文索引'
    case 'SPATIAL':
      return '空间索引'
    case 'UNIQUE':
      return '唯一索引'
    default:
      return '其他'
  }
}

// 加载索引数据
const loadIndexes = async () => {
  console.log(`[IndexViewModal] loadIndexes 被调用，props:`, {
    tableId: props.tableId,
    tableName: props.tableName,
    dataSourceId: props.dataSourceId,
    schema: props.schema
  });
  
  if (!props.tableId) {
    console.warn('[IndexViewModal] tableId 为空，无法加载索引信息');
    message.warning('表ID为空，无法加载索引信息');
    return;
  }
  
  isLoading.value = true
  
  try {
    console.log(`[IndexViewModal] 开始加载表 ${props.tableId} 的索引信息`);
    // 调用真实的API获取索引信息
    const apiIndexes = await dataSourceService.getTableIndexes(props.tableId)
    
    indexes.value = apiIndexes
    console.log(`[IndexViewModal] 成功加载 ${apiIndexes.length} 个索引`);
    message.success('索引信息加载成功')
  } catch (error) {
    console.error('加载索引信息失败:', error)
    message.error('加载索引信息失败')
    indexes.value = []
  } finally {
    isLoading.value = false
  }
}

// 生成模拟索引数据
const generateMockIndexes = (tableName: string): IndexMetadata[] => {
  // 根据表名生成不同的索引数据
  if (tableName.includes('user') || tableName.includes('oauth')) {
    return [
      {
        name: 'PRIMARY',
        type: 'BTREE',
        columns: ['id'],
        unique: true
      },
      {
        name: 'idx_username',
        type: 'BTREE',
        columns: ['username'],
        unique: true
      },
      {
        name: 'idx_email',
        type: 'BTREE',
        columns: ['email'],
        unique: true
      },
      {
        name: 'idx_created_at',
        type: 'BTREE',
        columns: ['created_at'],
        unique: false
      },
      {
        name: 'idx_status_created',
        type: 'BTREE',
        columns: ['status', 'created_at'],
        unique: false
      }
    ]
  } else if (tableName.includes('order') || tableName.includes('payment')) {
    return [
      {
        name: 'PRIMARY',
        type: 'BTREE',
        columns: ['id'],
        unique: true
      },
      {
        name: 'idx_user_id',
        type: 'BTREE',
        columns: ['user_id'],
        unique: false
      },
      {
        name: 'idx_order_no',
        type: 'BTREE',
        columns: ['order_no'],
        unique: true
      },
      {
        name: 'idx_status_created',
        type: 'BTREE',
        columns: ['status', 'created_at'],
        unique: false
      },
      {
        name: 'idx_amount',
        type: 'BTREE',
        columns: ['amount'],
        unique: false
      }
    ]
  } else if (tableName.includes('product') || tableName.includes('goods')) {
    return [
      {
        name: 'PRIMARY',
        type: 'BTREE',
        columns: ['id'],
        unique: true
      },
      {
        name: 'idx_category_id',
        type: 'BTREE',
        columns: ['category_id'],
        unique: false
      },
      {
        name: 'idx_name',
        type: 'FULLTEXT',
        columns: ['name', 'description'],
        unique: false
      },
      {
        name: 'idx_price',
        type: 'BTREE',
        columns: ['price'],
        unique: false
      },
      {
        name: 'idx_status_category',
        type: 'HASH',
        columns: ['status', 'category_id'],
        unique: false
      }
    ]
  } else {
    // 默认索引结构
    return [
      {
        name: 'PRIMARY',
        type: 'BTREE',
        columns: ['id'],
        unique: true
      },
      {
        name: 'idx_name',
        type: 'BTREE',
        columns: ['name'],
        unique: false
      },
      {
        name: 'idx_created_at',
        type: 'BTREE',
        columns: ['created_at'],
        unique: false
      },
      {
        name: 'idx_updated_at',
        type: 'BTREE',
        columns: ['updated_at'],
        unique: false
      }
    ]
  }
}



// 关闭模态框
const handleClose = () => {
  emit('update:visible', false)
}

// 获取排序后的列
const getSortedColumns = (columns: IndexColumn[]) => {
  return [...columns].sort((a, b) => a.position - b.position);
};
</script>

<style scoped>
.index-view-modal {
  max-height: 70vh;
  overflow-y: auto;
}
</style>
