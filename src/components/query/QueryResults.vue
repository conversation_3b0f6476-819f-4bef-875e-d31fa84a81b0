<script setup lang="ts">
import {computed, onMounted, onUnmounted, ref, watch} from 'vue'
import type {QueryResult, QueryStatus} from '@/types/query'
import QueryVisualization from './QueryVisualization.vue'
import QueryAnalysis from './QueryAnalysis.vue'
import QueryExecutionPlan from './QueryExecutionPlan.vue'
import {message} from 'ant-design-vue'

// 定义组件属性
const props = defineProps<{
  results: QueryResult | null
  isLoading: boolean
  error: string | null
  queryId?: string // 添加queryId属性
  isNaturalLanguageQuery?: boolean // 添加是否为自然语言查询的标志
  // 执行计划相关参数
  dataSourceId?: string
  sql?: string
  queryType?: string
  parameters?: Record<string, any>
  schemaId?: string
}>()

// 定义组件事件
const emit = defineEmits<{
  (e: 'export-results', format: 'excel'): void
  (e: 'apply-suggestion', query: string): void // 添加应用建议事件
  (e: 'cancel', queryId: string): void
  (e: 'page-change', page: number): void // 页码变化事件
  (e: 'page-size-change', size: number): void // 每页条数变化事件
}>()

// 分页状态
const currentPage = ref(1)
const pageSize = ref(10)
const pageSizeOptions = [10, 20, 50, 100, 200, 300, 500, 1000]
const isPaginationLoading = ref(false) // 分页加载状态

// 是否显示表格列配置
const showColumnConfig = ref(false)


// 跟踪隐藏的列
const hiddenColumns = ref<string[]>([])

// 批量选择模式相关
const selectedColumns = ref<string[]>([]) // 批量选择模式下的选中列
const isBatchMode = ref(false) // 是否处于批量选择模式

// 跟踪展示给用户的错误消息
const userFriendlyError = ref('')

// 当前激活的视图 (table、visualization、analysis 或 execution-plan)
const activeView = ref<'table' | 'visualization' | 'analysis' | 'execution-plan'>('table')


// 查询开始运行时间计时器
const executionTime = ref(0)
let executionTimer: number | null = null

// 格式化运行时间
const formatRunningTime = (ms: number) => {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  if (minutes > 0) {
    return `${minutes}分钟 ${seconds % 60}秒`
  }
  return `${seconds}秒`
}

// 取消查询执行
const cancelQuery = () => {
  if (!props.queryId) return

  emit('cancel', props.queryId)
  stopTimer()
}

// 开始计时器
const startTimer = () => {
  if (executionTimer) return

  const startTime = Date.now()
  executionTimer = window.setInterval(() => {
    executionTime.value = Date.now() - startTime
  }, 1000)
}

// 停止计时器
const stopTimer = () => {
  if (executionTimer) {
    window.clearInterval(executionTimer)
    executionTimer = null
  }
  executionTime.value = 0
}

// 监听加载状态，开始或停止计时器
watch(() => props.isLoading, (newValue) => {
  if (newValue) {
    startTimer()
  } else {
    stopTimer()
  }
})

// 这里不需要调试函数的定义，已在下方定义

// 组件卸载时清理计时器
onUnmounted(() => {
  stopTimer()
})

// 计算属性：可见的列和列映射关系
const visibleColumnsAndMapping = computed(() => {
  if (!props.results) return { visibleColumns: [], columnMapping: {} }

  // 创建字段映射表
  const mapping: Record<string, any> = {}
  let columns: string[] = []

  // 处理后端返回的标准数据结构，包含data.data.fields数组格式
  if (props.results.data && props.results.data.data && props.results.data.data.fields && Array.isArray(props.results.data.data.fields)) {

    props.results.data.data.fields.forEach(field => {
      if (typeof field === 'string') {
        columns.push(field)
        mapping[field] = { FIELD: field, LABEL: field }
      } else if (typeof field === 'object' && field !== null) {
        // 尝试获取字段名称，优先使用name，然后是FIELD，field，label等
        const fieldName = field.name || field.FIELD || field.field || field.label || field.LABEL ||
                         (field.type && field.type === 'String' && field.name ? field.name : '未知字段')
        columns.push(fieldName)
        mapping[fieldName] = {
          FIELD: fieldName,
          LABEL: field.label || field.LABEL || fieldName,
          TYPE: field.type || field.TYPE || 'string'
        }
      }
    })

    return {
      visibleColumns: columns.filter(col => !hiddenColumns.value.includes(col)),
      columnMapping: mapping
    }
  }

  // 优先处理直接包含在根对象中的columns数组
  if (props.results.columns && Array.isArray(props.results.columns)) {

    props.results.columns.forEach(column => {
      if (typeof column === 'string') {
        columns.push(column)
        mapping[column] = { FIELD: column, LABEL: column }
      } else if (typeof column === 'object' && column !== null) {
        // 处理列对象格式
        const columnName = column.name || column.field || column.FIELD || column
        columns.push(columnName)
        mapping[columnName] = {
          FIELD: columnName,
          LABEL: column.label || column.LABEL || columnName,
          TYPE: column.type || column.TYPE || 'string'
        }
      }
    })

    return {
      visibleColumns: columns.filter(col => !hiddenColumns.value.includes(col)),
      columnMapping: mapping
    }
  }

  // 处理后端返回的data.columns数组（这是API实际返回的格式）
  if (props.results.data && props.results.data.columns && Array.isArray(props.results.data.columns)) {

    props.results.data.columns.forEach(column => {
      if (typeof column === 'string') {
        columns.push(column)
        mapping[column] = { FIELD: column, LABEL: column }
      } else if (typeof column === 'object' && column !== null) {
        // 处理列对象格式
        const columnName = column.name || column.field || column.FIELD || column
        columns.push(columnName)
        mapping[columnName] = {
          FIELD: columnName,
          LABEL: column.label || column.LABEL || columnName,
          TYPE: column.type || column.TYPE || 'string'
        }
      }
    })

    return {
      visibleColumns: columns.filter(col => !hiddenColumns.value.includes(col)),
      columnMapping: mapping
    }
  }

  // 处理后端返回的标准数据结构，包含data.fields[{FIELD,LABEL等}]格式
  if (props.results.data && props.results.data.fields && Array.isArray(props.results.data.fields)) {

    props.results.data.fields.forEach(field => {
      // 如果字段是字符串直接使用
      if (typeof field === 'string') {
        columns.push(field)
        mapping[field] = field
        return
      }

      // 如果包含FIELD属性的对象（后端特定格式）
      if (field.FIELD) {
        columns.push(field.FIELD)
        // 存储完整的字段信息
        mapping[field.FIELD] = field
        return
      }

      // 兼容其他可能的字段名格式
      const fieldName = field.name || field.field || field.label || field.LABEL || '未知字段'
      columns.push(fieldName)
      mapping[fieldName] = field
    })

    return {
      visibleColumns: columns.filter(col => !hiddenColumns.value.includes(col)),
      columnMapping: mapping
    }
  }

  // 处理API直接返回的fields (可能是对象数组或字符串数组)
  if (props.results.fields && Array.isArray(props.results.fields)) {

    props.results.fields.forEach(field => {
      if (typeof field === 'string') {
        columns.push(field)
        mapping[field] = field
        return
      }

      if (field.FIELD) {
        columns.push(field.FIELD)
        mapping[field.FIELD] = field
        return
      }

      const fieldName = field.name || field.field || field.label || field.LABEL || '未知字段'
      columns.push(fieldName)
      mapping[fieldName] = field
    })

    return {
      visibleColumns: columns.filter(col => !hiddenColumns.value.includes(col)),
      columnMapping: mapping
    }
  }

  // 从第一行数据推断列名（即使columns字段为空数组）
  if (props.results.rows && Array.isArray(props.results.rows) && props.results.rows.length > 0) {

    const firstRow = props.results.rows[0]
    if (typeof firstRow === 'object' && firstRow !== null) {
      columns = Object.keys(firstRow)

      columns.forEach(col => {
        mapping[col] = { FIELD: col, LABEL: col }
      })

      return {
        visibleColumns: columns.filter(col => !hiddenColumns.value.includes(col)),
        columnMapping: mapping
      }
    }
  }

  // 尝试从data.data.rows的第一行推断列名
  if (props.results.data && props.results.data.data && props.results.data.data.rows && Array.isArray(props.results.data.data.rows) && props.results.data.data.rows.length > 0) {

    const firstRow = props.results.data.data.rows[0]
    if (typeof firstRow === 'object' && firstRow !== null) {
      columns = Object.keys(firstRow)

      columns.forEach(col => {
        mapping[col] = { FIELD: col, LABEL: col }
      })

      return {
        visibleColumns: columns.filter(col => !hiddenColumns.value.includes(col)),
        columnMapping: mapping
      }
    }
  }

  // 如果都找不到，返回空数组
  console.warn('无法确定数据列，返回空数组')
  return { visibleColumns: [], columnMapping: {} }
})

// 从计算属性中解构出visibleColumns和columnMapping
const visibleColumns = computed(() => visibleColumnsAndMapping.value.visibleColumns)
const columnMapping = computed(() => visibleColumnsAndMapping.value.columnMapping)

// 计算属性：总页数
const totalPages = computed(() => {
  if (!props.results) return 1

  // 优先使用后端返回的总页数
  if (props.results.data?.totalPages !== undefined) {
    return props.results.data.totalPages
  }

  // 获取总记录数
  const count = totalRecords.value

  // 如果总记录数为0，则只有1页
  if (count === 0) return 1

  // 计算总页数（向上取整）
  return Math.ceil(count / pageSize.value)
})

// 计算属性：当前页的行数据（直接使用后端返回的数据，不做客户端分页）
const paginatedRows = computed(() => {

  // 如果没有结果数据，返回空数组
  if (!props.results) {
    return []
  }

  let rows: any[] = []

  // 检查data.data.rows结构（与后端API响应匹配）
  if (props.results.data && props.results.data.data && props.results.data.data.rows) {
    if (Array.isArray(props.results.data.data.rows)) {
      rows = props.results.data.data.rows
    } else if (typeof props.results.data.data.rows === 'object') {
      // 尝试将对象转为数组
      rows = Object.values(props.results.data.data.rows)
    }
  }
  // 检查data.rows结构
  else if (props.results.data && props.results.data.rows) {
    if (Array.isArray(props.results.data.rows)) {
      rows = props.results.data.rows
    } else if (typeof props.results.data.rows === 'object') {
      // 尝试将对象转为数组
      rows = Object.values(props.results.data.rows)
    }
  }
  // 直接使用rows属性
  else if (props.results.rows) {
    if (Array.isArray(props.results.rows)) {
      rows = props.results.rows
    } else if (typeof props.results.rows === 'object') {
      // 尝试将对象转为数组
      rows = Object.values(props.results.rows)
    }
  }

  // 如果rows不是数组或为空，返回空数组
  if (!Array.isArray(rows) || rows.length === 0) {
    console.warn('行数据不是数组或为空')
    return []
  }

  // 直接返回后端返回的当前页数据，不做客户端分页
  return rows
})

// 计算总记录数
const totalRecords = computed(() => {
  if (!props.results) return 0

  // 优先使用后端返回的总记录数
  if (props.results.data?.total !== undefined) {
    return props.results.data.total
  }

  // 检查其他可能的位置
  if (props.results.total !== undefined) {
    return props.results.total
  }

  // 检查data.data.rowCount
  if (props.results.data?.data?.rowCount !== undefined) {
    return props.results.data.data.rowCount
  }

  // 检查其他可能的位置
  if (props.results.data?.totalCount !== undefined) {
    return props.results.data.totalCount
  }
  if (props.results.rowCount !== undefined) {
    return props.results.rowCount
  }

  // 通过计算行数组长度获取（仅作为后备方案）
  if (props.results.data?.data?.rows && Array.isArray(props.results.data.data.rows)) {
    return props.results.data.data.rows.length
  }
  if (props.results.data?.rows && Array.isArray(props.results.data.rows)) {
    return props.results.data.rows.length
  }
  if (props.results.rows && Array.isArray(props.results.rows)) {
    return props.results.rows.length
  }

  console.warn('无法确定总记录数，返回0')
  return 0
})

// 计算实际返回的数据条数（当前页的数据条数）
const actualReturnedRecords = computed(() => {
  return paginatedRows.value.length
})

// 调试函数：输出数据结构到控制台
const debugDataStructure = () => {
  if (!props.results) {
    return
  }

  // 检查顶层结构
  // 检查data属性
  // 检查rows属性
  // 检查columns属性
  // 检查fields属性

  // 检查data结构
  if (props.results.data) {
    // 检查data类型
    // 检查data包含rows
    // 检查data包含columns
    // 检查data包含fields
    // 检查data包含data

    // 检查data.data结构
    if (props.results.data.data) {
      // 检查data.data类型
      // 检查data.data包含rows
      // 检查data.data包含fields

      // 检查data.data.rows
      if (props.results.data.data.rows) {
        // 检查data.data.rows类型
        // 检查data.data.rows是否为数组
        if (Array.isArray(props.results.data.data.rows)) {
          // 检查data.data.rows长度
          if (props.results.data.data.rows.length > 0) {
            // 检查data.data.rows第一项
          }
        } else {
          // 检查data.data.rows键
        }
      }

      // 检查data.data.fields
      if (props.results.data.data.fields) {
        // 检查data.data.fields类型
        // 检查data.data.fields是否为数组
        if (Array.isArray(props.results.data.data.fields)) {
          // 检查data.data.fields长度
          if (props.results.data.data.fields.length > 0) {
            // 检查data.data.fields第一项
          }
        }
      }
    }
  }

  // 检查计算属性
  // 检查visibleColumns值
  // 检查visibleColumns长度
  // 检查paginatedRows值
  // 检查paginatedRows长度
  // 检查totalRecords
  // 检查totalPages
}

// 在结果变化时调试数据结构 - 已合并到下方的watch函数

// 切换列可见性
const toggleColumnVisibility = (column: string) => {
  if (isBatchMode.value) {
    // 批量选择模式：添加到选中列表
    const index = selectedColumns.value.indexOf(column)
    if (index > -1) {
      selectedColumns.value.splice(index, 1)
    } else {
      selectedColumns.value.push(column)
    }
  } else {
    // 直接切换模式：直接显示/隐藏
    if (hiddenColumns.value.includes(column)) {
      hiddenColumns.value = hiddenColumns.value.filter(col => col !== column)
    } else {
      hiddenColumns.value.push(column)
    }
  }
}

// 显示所有列
const showAllColumns = () => {
  hiddenColumns.value = []
}

// 全选列
const selectAllColumns = () => {
  hiddenColumns.value = []
}

// 全不选列
const deselectAllColumns = () => {
  hiddenColumns.value = [...visibleColumns.value]
}

// 隐藏所有列
const hideAllColumns = () => {
  hiddenColumns.value = [...visibleColumns.value]
}


// 进入批量选择模式
const enterBatchMode = () => {
  isBatchMode.value = true
  selectedColumns.value = []
}

// 退出批量选择模式
const exitBatchMode = () => {
  isBatchMode.value = false
  selectedColumns.value = []
}

// 批量显示选中的列
const batchShowSelected = () => {
  // 先将所有列都隐藏
  hiddenColumns.value = [...visibleColumns.value]
  // 然后将选中的列从隐藏列表中移除（即显示它们）
  selectedColumns.value.forEach(column => {
    const index = hiddenColumns.value.indexOf(column)
    if (index > -1) {
      hiddenColumns.value.splice(index, 1)
    }
  })
  exitBatchMode()
}

// 批量隐藏选中的列
const batchHideSelected = () => {
  // 将选中的列添加到隐藏列表
  selectedColumns.value.forEach(column => {
    if (!hiddenColumns.value.includes(column)) {
      hiddenColumns.value.push(column)
    }
  })
  exitBatchMode()
}

// 获取列项样式类
const getColumnItemClass = (column: string) => {
  if (isBatchMode.value) {
    // 批量选择模式：根据是否选中显示不同样式
    return selectedColumns.value.includes(column) 
      ? 'border-blue-300 bg-blue-50' 
      : 'border-gray-200 bg-white'
  } else {
    // 直接模式：根据是否隐藏显示不同样式
    return hiddenColumns.value.includes(column) 
      ? 'border-gray-200 bg-gray-50' 
      : 'border-indigo-200 bg-indigo-50'
  }
}

// 获取列图标样式类
const getColumnIconClass = (column: string) => {
  if (isBatchMode.value) {
    // 批量选择模式：显示选择框
    return selectedColumns.value.includes(column) 
      ? 'fas fa-check-square text-blue-600' 
      : 'far fa-square text-gray-400'
  } else {
    // 直接模式：显示显示/隐藏状态
    return hiddenColumns.value.includes(column) 
      ? 'far fa-square text-gray-400' 
      : 'fas fa-check-square text-indigo-600'
  }
}

// 获取列文本样式类
const getColumnTextClass = (column: string) => {
  if (isBatchMode.value) {
    // 批量选择模式：根据是否选中显示不同颜色
    return selectedColumns.value.includes(column) 
      ? 'text-blue-700' 
      : 'text-gray-500'
  } else {
    // 直接模式：根据是否隐藏显示不同颜色
    return hiddenColumns.value.includes(column) 
      ? 'text-gray-500' 
      : 'text-gray-700'
  }
}

// 获取列状态图标样式类
const getColumnStatusIconClass = (column: string) => {
  if (isBatchMode.value) {
    // 批量选择模式：显示选择状态
    return selectedColumns.value.includes(column) 
      ? 'fas fa-check text-blue-600' 
      : 'far fa-circle text-gray-400'
  } else {
    // 直接模式：显示显示/隐藏状态
    return hiddenColumns.value.includes(column) 
      ? 'fas fa-eye-slash text-gray-400' 
      : 'fas fa-eye text-indigo-600'
  }
}

// 切换到指定页
const goToPage = (page: number) => {
  if (page < 1) return
  if (page === currentPage.value) return // 避免重复请求

  isPaginationLoading.value = true
  currentPage.value = page
  emit('page-change', page)
}

// 更新每页显示数量
const updatePageSize = (size: number) => {
  
  // 移除这个条件判断，因为v-model可能已经更新了pageSize的值
  // if (size === pageSize.value) return // 避免重复请求

  isPaginationLoading.value = true
  currentPage.value = 1 // 重置为第一页
  pageSize.value = size
  emit('page-size-change', size)
}

// 导出数据
const exportData = (format: 'excel') => {
  emit('export-results', format)
}

// 处理优化建议应用
const handleApplySuggestion = (query: string) => {
  emit('apply-suggestion', query)
}

// 重置分页加载状态
const resetPaginationLoading = () => {
  isPaginationLoading.value = false
}

// 监听分页变化，确保加载状态被重置
watch(() => props.results, () => {
  // 当查询结果更新时，重置分页加载状态
  resetPaginationLoading()
}, { deep: true })

// 监听分页完成信号
watch(() => props.results?._paginationComplete, () => {
  // 当收到分页完成信号时，重置分页加载状态
  resetPaginationLoading()
})

// 格式化执行时间
const formatExecutionTime = (ms: number | undefined): string => {
  if (!ms) return '0 ms'

  if (ms < 1000) {
    return `${ms} ms`
  } else if (ms < 60000) {
    return `${(ms / 1000).toFixed(2)} 秒`
  } else {
    const minutes = Math.floor(ms / 60000)
    const seconds = ((ms % 60000) / 1000).toFixed(2)
    return `${minutes} 分 ${seconds} 秒`
  }
}

// 处理错误信息的展示
watch(() => props.error, (error) => {
  if (error) {
    // 检查是否是被取消的查询
    if (error.includes('取消') || error.toLowerCase().includes('cancel')) {
      userFriendlyError.value = '查询已被取消'
    }
    // 检查是否是SQL语法错误
    else if (error.includes('syntax error')) {
      userFriendlyError.value = 'SQL语法错误，请检查您的查询语句'
    }
    // 检查是否是表不存在错误
    else if (error.includes('table') && error.includes('not exist')) {
      userFriendlyError.value = '表不存在，请检查表名是否正确'
    }
    // 检查是否是字段不存在错误
    else if (error.includes('column') && error.includes('not exist')) {
      userFriendlyError.value = '字段不存在，请检查字段名是否正确'
    }
    // 默认错误信息
    else {
      userFriendlyError.value = error
    }
  } else {
    userFriendlyError.value = ''
  }
})

// 辅助函数：处理查询结果
const processQueryResult = (results: any) => {
  if (!results) return;

  // 检查是否有rows数据
  if (results.rows && Array.isArray(results.rows) && results.rows.length > 0) {
    // 如果columns为空，但rows有数据，从rows提取列名
    if (!results.columns || (Array.isArray(results.columns) && results.columns.length === 0)) {
      const firstRow = results.rows[0];
      if (typeof firstRow === 'object' && firstRow !== null) {
        const extractedColumns = Object.keys(firstRow).map(key => {
          return {
            name: key,
            label: key,
            type: typeof firstRow[key]
          };
        });

        results.columns = extractedColumns;
      }
    }
  }

  return results;
}

// 当结果改变时，处理数据并调试
watch(() => props.results, (newResults) => {
  if (newResults) {
    // 处理查询结果数据
    processQueryResult(newResults);

    // 特殊处理：如果有rows数据但columns为空，确保能从rows提取列信息
    if (newResults.rows && Array.isArray(newResults.rows) && newResults.rows.length > 0 &&
        (!newResults.columns || (Array.isArray(newResults.columns) && newResults.columns.length === 0))) {
      // 这里不需要操作，visibleColumnsAndMapping计算属性会处理这种情况
    }

    // 检查是否有嵌套的data.data结构
    if (newResults.data && newResults.data.data) {

      // 处理嵌套的data.data.rows
      if (newResults.data.data.rows) {
        if (!Array.isArray(newResults.data.data.rows) && typeof newResults.data.data.rows === 'object') {
          try {
            const rows = []
            for (const key in newResults.data.data.rows) {
              if (Object.prototype.hasOwnProperty.call(newResults.data.data.rows, key)) {
                rows.push(newResults.data.data.rows[key])
              }
            }
            newResults.data.data.rows = rows
          } catch (e) {
            console.error("转换data.data.rows失败:", e)
          }
        }
      }

      // 处理嵌套的data.data.fields
      if (newResults.data.data.fields) {
        if (!Array.isArray(newResults.data.data.fields) && typeof newResults.data.data.fields === 'object') {
          try {
            const fields = []
            for (const key in newResults.data.data.fields) {
              if (Object.prototype.hasOwnProperty.call(newResults.data.data.fields, key)) {
                const field = newResults.data.data.fields[key]
                if (typeof field === 'object') {
                  field.name = field.name || key
                  fields.push(field)
                } else {
                  fields.push({ name: key, type: typeof field })
                }
              }
            }
            newResults.data.data.fields = fields
          } catch (e) {
            console.error("转换data.data.fields失败:", e)
          }
        }
      }
    }

    // 检查是否有直接的rows和columns
    if (newResults.rows) {
      // 确保即使返回数据的格式不完全符合期望，组件也能尝试显示数据
      if (!Array.isArray(newResults.rows) && typeof newResults.rows === 'object') {
        try {
          // 尝试将对象转换为数组
          const rows = [];
          if (newResults.rows && Object.keys(newResults.rows).length > 0) {
            for (const key in newResults.rows) {
              if (Object.prototype.hasOwnProperty.call(newResults.rows, key)) {
                rows.push(newResults.rows[key]);
              }
            }
            newResults.rows = rows;
          }
        } catch (e) {
          console.error("Failed to convert rows to array format:", e);
        }
      }
    }

    if (newResults.columns) {
      // 确保即使返回数据的格式不完全符合期望，组件也能尝试显示数据
      if (!Array.isArray(newResults.columns) && typeof newResults.columns === 'object') {
        try {
          // 尝试将对象转换为数组
          const columns = [];
          if (newResults.columns && Object.keys(newResults.columns).length > 0) {
            for (const key in newResults.columns) {
              if (Object.prototype.hasOwnProperty.call(newResults.columns, key)) {
                columns.push(newResults.columns[key]);
              }
            }
            newResults.columns = columns;
          }
        } catch (e) {
          console.error("Failed to convert columns to array format:", e);
        }
      }
    }

    // 检查是否有data对象
    if (newResults.data) {
      // 如果data.rows存在但不是数组，尝试转换
      if (newResults.data.rows && !Array.isArray(newResults.data.rows) && typeof newResults.data.rows === 'object') {
        try {
          const rows = [];
          for (const key in newResults.data.rows) {
            if (Object.prototype.hasOwnProperty.call(newResults.data.rows, key)) {
              rows.push(newResults.data.rows[key]);
            }
          }
          newResults.data.rows = rows;
        } catch (e) {
          console.error("转换data.rows失败:", e);
        }
      }
    }

    // 自动调用调试函数，确保能看到数据结构
    debugDataStructure();

    // 额外检查paginatedRows和visibleColumns
  }

  // 重置分页和列配置
  if (newResults) {
    // 从后端响应中获取当前页码，如果没有则重置为1
    const backendPage = newResults.data?.page || newResults.page || 1
    const backendSize = newResults.data?.size || newResults.size

    currentPage.value = backendPage

    // 从后端响应中获取页面大小，如果没有则保持当前设置
    if (backendSize && backendSize !== pageSize.value) {
      pageSize.value = backendSize
    }
  } else {
    currentPage.value = 1
  }

  hiddenColumns.value = []
  // 重置分页加载状态
  resetPaginationLoading()
}, { immediate: true }) // 添加immediate: true，确保组件创建时立即执行

// 获取单元格值的函数
const getCellValue = (row: any, columnName: string): any => {
  // 如果行对象为空，直接返回null
  if (!row) return null

  // 直接尝试使用列名
  if (row[columnName] !== undefined) {
    return row[columnName]
  }

  // 尝试使用小写列名
  const lowerColumnName = columnName.toLowerCase()
  if (row[lowerColumnName] !== undefined) {
    return row[lowerColumnName]
  }

  // 尝试使用大写列名
  const upperColumnName = columnName.toUpperCase()
  if (row[upperColumnName] !== undefined) {
    return row[upperColumnName]
  }

  // 如果是嵌套路径（如 "user.name"），尝试访问嵌套属性
  if (columnName.includes('.')) {
    const parts = columnName.split('.')
    let value = row
    for (const part of parts) {
      if (value === null || value === undefined) return null
      value = value[part]
    }
    return value
  }

  // 遍历对象所有键，执行不区分大小写的比较
  const rowKeys = Object.keys(row)
  for (const key of rowKeys) {
    if (key.toLowerCase() === lowerColumnName) {
      return row[key]
    }
  }

  // 尝试使用列映射中的FIELD属性
  if (columnMapping.value[columnName] && columnMapping.value[columnName].FIELD) {
    const fieldName = columnMapping.value[columnName].FIELD
    if (row[fieldName] !== undefined) {
      return row[fieldName]
    }
  }

  // 找不到时返回null
  return null
}

// 格式化单元格内容
const formatCellValue = (value: any): string => {
  // 处理null或undefined值
  if (value === null || value === undefined) {
    return 'NULL'
  }

  // 处理字符串类型的值
  if (typeof value === 'string') {
    // 检测是否是JSON字符串
    if ((value.startsWith('{') && value.endsWith('}')) ||
        (value.startsWith('[') && value.endsWith(']'))) {
      try {
        // 尝试解析JSON字符串
        const jsonObj = JSON.parse(value)
        return JSON.stringify(jsonObj)
      } catch (e) {
        // 解析失败返回原字符串
        return value
      }
    }

    // 检查空字符串
    if (value.trim() === '') {
      return '(空字符串)'
    }

    return value
  }

  // 处理对象类型的值
  if (typeof value === 'object' && value !== null) {
    // 处理日期对象
    if (value instanceof Date) {
      return value.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      })
    }

    try {
      // 尝试将对象转为JSON字符串
      return JSON.stringify(value)
    } catch (e) {
      return String(value)
    }
  }

  // 处理布尔值
  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }

  // 其他情况直接转为字符串
  return String(value)
}

// 屏幕宽度响应式状态
const screenWidth = ref(window.innerWidth)

// 列宽状态管理
const columnWidths = ref<Record<string, number>>({})
const isResizing = ref(false)
const resizingColumn = ref<string | null>(null)
const startX = ref(0)
const startWidth = ref(0)

// 获取响应式列宽样式 - 使用计算属性
const columnStyle = computed(() => {
  if (screenWidth.value >= 2560) {
    // 4K屏幕 - 显示更多列
    return 'min-width: 35px; max-width: 80px;'
  } else if (screenWidth.value >= 1600) {
    // 超大屏幕
    return 'min-width: 40px; max-width: 100px;'
  } else if (screenWidth.value >= 1200) {
    // 大屏幕
    return 'min-width: 50px; max-width: 120px;'
  } else {
    // 普通屏幕
    return 'min-width: 80px; max-width: 200px;'
  }
})

// 获取列头样式 - 确保列名完整显示
const getColumnHeaderStyle = () => {
  if (screenWidth.value >= 2560) {
    // 4K屏幕 - 列头需要更多空间显示完整名称
    return 'min-width: 60px; max-width: 120px; min-height: 40px;'
  } else if (screenWidth.value >= 1600) {
    // 超大屏幕
    return 'min-width: 70px; max-width: 150px; min-height: 40px;'
  } else if (screenWidth.value >= 1200) {
    // 大屏幕
    return 'min-width: 80px; max-width: 180px; min-height: 40px;'
  } else {
    // 普通屏幕
    return 'min-width: 100px; max-width: 250px; min-height: 40px;'
  }
}

// 获取列的实际宽度
const getColumnWidth = (column: string) => {
  if (columnWidths.value[column]) {
    return `${columnWidths.value[column]}px`
  }
  
  // 根据列名长度动态计算列宽
  const columnName = columnMapping[column]?.LABEL || column
  const baseWidth = Math.max(columnName.length * 10, 100) // 每个字符10px，最小100px
  
  // 根据屏幕大小调整
  if (screenWidth.value >= 2560) {
    return `${Math.min(baseWidth, 300)}px`
  } else if (screenWidth.value >= 1600) {
    return `${Math.min(baseWidth, 250)}px`
  } else if (screenWidth.value >= 1200) {
    return `${Math.min(baseWidth, 200)}px`
  } else {
    return `${Math.min(baseWidth, 180)}px`
  }
}

// 开始拖拽调整列宽
const startResize = (column: string, event: MouseEvent) => {
  isResizing.value = true
  resizingColumn.value = column
  startX.value = event.clientX
  startWidth.value = columnWidths.value[column] || 120
  
  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
  event.preventDefault()
  event.stopPropagation()
}

// 处理拖拽调整
const handleResize = (event: MouseEvent) => {
  if (!isResizing.value || !resizingColumn.value) return
  
  const deltaX = event.clientX - startX.value
  const newWidth = Math.max(50, startWidth.value + deltaX) // 最小宽度50px
  
  columnWidths.value[resizingColumn.value] = newWidth
}

// 停止拖拽调整
const stopResize = () => {
  isResizing.value = false
  resizingColumn.value = null
  
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
}

// 监听窗口大小变化
const handleWindowResize = () => {
  screenWidth.value = window.innerWidth
}

// 组件挂载时添加监听器
onMounted(() => {
  window.addEventListener('resize', handleWindowResize)
})

// 组件卸载时移除监听器
onUnmounted(() => {
  window.removeEventListener('resize', handleWindowResize)
})
</script>

<template>
  <div class="h-full flex flex-col bg-white">
    <!-- 标签选择器 -->
    <div class="flex border-b border-gray-200">
      <button
        @click="activeView = 'table'"
        :class="[
          'px-4 py-2 text-sm font-medium border-b-2 focus:outline-none',
          activeView === 'table'
            ? 'border-indigo-500 text-indigo-600'
            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
        ]"
      >
        <i class="fas fa-table mr-2"></i>
        表格
      </button>

      <button
        @click="() => {}"
        :class="[
          'px-4 py-2 text-sm font-medium border-b-2 focus:outline-none cursor-not-allowed',
          'border-transparent text-gray-300 bg-gray-100'
        ]"
        disabled
      >
        <i class="fas fa-chart-bar mr-2"></i>
        可视化
      </button>

      <button
        @click="() => {}"
        :class="[
          'px-4 py-2 text-sm font-medium border-b-2 focus:outline-none cursor-not-allowed',
          'border-transparent text-gray-300 bg-gray-100'
        ]"
        disabled
      >
        <i class="fas fa-microscope mr-2"></i>
        分析
      </button>

      <button
        @click="activeView = 'execution-plan'"
        :class="[
          'px-4 py-2 text-sm font-medium border-b-2 focus:outline-none',
          activeView === 'execution-plan'
            ? 'border-indigo-500 text-indigo-600'
            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
        ]"
      >
        <i class="fas fa-project-diagram mr-2"></i>
        查看执行计划
      </button>
    </div>

    <!-- 加载状态 - 等待获取结果 -->
    <div v-if="isLoading" class="flex-1 flex items-center justify-center">
      <div class="text-center text-gray-500">
        <div class="w-8 h-8 border-2 border-gray-300 border-t-indigo-500 rounded-full animate-spin mx-auto mb-2"></div>
        <p>准备展示结果...</p>
      </div>
    </div>
    <!-- 无结果 -->
    <div v-else-if="!results || ((!paginatedRows || paginatedRows.length === 0) && (!visibleColumns || visibleColumns.length === 0))" class="flex-1 flex items-center justify-center p-4">
      <div class="text-center text-gray-500">
        <i class="fas fa-database text-gray-400 text-3xl mb-2"></i>
        <p>没有查询结果</p>
        <p class="text-sm mt-1">请修改查询条件或选择其他数据</p>

        <!-- 调试按钮 - 仅输出到控制台 -->
        <div class="mt-4">
          <button
            @click="debugDataStructure"
            class="px-3 py-1 bg-blue-50 text-blue-600 border border-blue-200 rounded text-sm hover:bg-blue-100"
          >
            <i class="fas fa-bug mr-1"></i>
            在控制台查看数据结构
          </button>
        </div>

        <!-- 调试信息面板 -->
        <div v-if="results" class="mt-3 p-2 bg-gray-100 rounded text-xs text-left overflow-auto max-h-40 max-w-md">
          <p class="font-bold">调试信息：</p>
          <p>结果对象类型: {{ typeof results }}</p>
          <p>结果包含data: {{ results.data ? '是' : '否' }}</p>
          <p>结果包含data.data: {{ results.data && results.data.data ? '是' : '否' }}</p>
          <p>直接rows存在: {{ results.rows ? '是' : '否' }}</p>
          <p>直接rows长度: {{ results.rows && Array.isArray(results.rows) ? results.rows.length : '非数组或不存在' }}</p>
          <p v-if="results.data">data.rows存在: {{ results.data.rows ? '是' : '否' }}</p>
          <p v-if="results.data && results.data.rows">data.rows长度: {{ Array.isArray(results.data.rows) ? results.data.rows.length : '非数组' }}</p>
          <p v-if="results.data && results.data.data">data.data.rows存在: {{ results.data.data.rows ? '是' : '否' }}</p>
          <p v-if="results.data && results.data.data && results.data.data.rows">data.data.rows长度: {{ Array.isArray(results.data.data.rows) ? results.data.data.rows.length : '非数组' }}</p>
          <p v-if="results.data && results.data.data && results.data.data.fields">data.data.fields存在: 是</p>
          <p v-if="results.data && results.data.data && results.data.data.fields && Array.isArray(results.data.data.fields)">data.data.fields长度: {{ results.data.data.fields.length }}</p>
          <p v-if="results.data && results.data.data && results.data.data.fields && Array.isArray(results.data.data.fields) && results.data.data.fields.length > 0">第一个field示例: {{ JSON.stringify(results.data.data.fields[0]) }}</p>
          <p>分页数据长度: {{ paginatedRows ? paginatedRows.length : '无分页数据' }}</p>
          <p>可见列长度: {{ visibleColumns ? visibleColumns.length : '无可见列' }}</p>
          <p v-if="results.data && results.data && results.data.data && results.data.data.rows && !Array.isArray(results.data.data.rows)">
            data.data.rows类型: {{ typeof results.data.data.rows }}
          </p>
          <div class="mt-2">
            <button
              @click="debugDataStructure"
              class="w-full px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs"
            >
              查看完整数据结构
            </button>
          </div>
        </div>
      </div>
    </div>
    <!-- 查询结果内容 -->
    <div v-else class="flex-1 flex flex-col overflow-hidden">
      <!-- 表格视图 -->
      <div v-if="activeView === 'table'" class="flex-1 overflow-auto">
        <div class="flex justify-between items-center px-4 py-2 bg-gray-50 border-b">
          <div class="flex items-center">
            <span class="text-sm text-gray-600">返回 {{ actualReturnedRecords }} 条结果</span>
            <span v-if="results.executionTime" class="ml-4 text-sm text-gray-600">
              执行时间: {{ formatExecutionTime(results.executionTime) }}
            </span>
          </div>

          <div class="flex items-center space-x-2">
            <!-- 列管理按钮 -->
            <div class="relative">
              <button
                @click="showColumnConfig = !showColumnConfig"
                class="px-3 py-1.5 border border-gray-300 rounded text-sm text-gray-600 hover:bg-gray-50 hover:border-indigo-300 transition-colors"
                :class="{'bg-indigo-50 border-indigo-300 text-indigo-600': showColumnConfig}"
              >
                <i class="fas fa-columns mr-1.5"></i>
                列管理
                <i class="fas fa-chevron-down ml-1 text-xs" :class="{'rotate-180': showColumnConfig}"></i>
              </button>
            </div>

            <!-- 导出按钮 - 只在非自然语言查询时显示 -->
            <div v-if="!isNaturalLanguageQuery" class="relative">
              <button
                @click="exportData('excel')"
                class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-600 hover:bg-gray-50"
              >
                <i class="fas fa-download mr-1"></i>
                导出为 Excel
              </button>
            </div>
          </div>
        </div>

        <!-- 列配置面板 -->
        <div v-if="showColumnConfig" class="p-4 bg-gradient-to-r from-gray-50 to-blue-50 border-b shadow-sm">
          <!-- 批量操作区域 -->
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-3">
              <h4 class="text-sm font-medium text-gray-700">列管理</h4>
              <div class="flex items-center space-x-2">
                <!-- 模式切换按钮 -->
                <button
                  v-if="!isBatchMode"
                  @click="enterBatchMode"
                  class="px-3 py-1.5 text-xs bg-blue-50 text-blue-600 border border-blue-200 rounded-md hover:bg-blue-100 transition-colors"
                >
                  <i class="fas fa-mouse-pointer mr-1"></i>
                  批量选择
                </button>
                
                <!-- 批量模式下的操作按钮 -->
                <template v-if="isBatchMode">
                  <button
                    @click="batchShowSelected"
                    :disabled="selectedColumns.length === 0"
                    class="px-3 py-1.5 text-xs bg-green-50 text-green-600 border border-green-200 rounded-md hover:bg-green-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <i class="fas fa-eye mr-1"></i>
                    显示选中 ({{ selectedColumns.length }})
                  </button>
                  <button
                    @click="batchHideSelected"
                    :disabled="selectedColumns.length === 0"
                    class="px-3 py-1.5 text-xs bg-red-50 text-red-600 border border-red-200 rounded-md hover:bg-red-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <i class="fas fa-eye-slash mr-1"></i>
                    隐藏选中 ({{ selectedColumns.length }})
                  </button>
                  <button
                    @click="exitBatchMode"
                    class="px-3 py-1.5 text-xs bg-gray-50 text-gray-600 border border-gray-200 rounded-md hover:bg-gray-100 transition-colors"
                  >
                    <i class="fas fa-times mr-1"></i>
                    取消
                  </button>
                </template>
                
                <!-- 快速操作按钮 -->
                <template v-if="!isBatchMode">
                  <button
                    @click="selectAllColumns"
                    class="px-3 py-1.5 text-xs bg-green-50 text-green-600 border border-green-200 rounded-md hover:bg-green-100 transition-colors"
                  >
                    <i class="fas fa-check-square mr-1"></i>
                    全选
                  </button>
                  <button
                    @click="deselectAllColumns"
                    class="px-3 py-1.5 text-xs bg-red-50 text-red-600 border border-red-200 rounded-md hover:bg-red-100 transition-colors"
                  >
                    <i class="fas fa-square mr-1"></i>
                    全不选
                  </button>
                </template>
              </div>
            </div>
            <div class="text-xs text-gray-500">
              <span v-if="!isBatchMode">
                已显示 {{ visibleColumns.length - hiddenColumns.length }} / {{ visibleColumns.length }} 列
              </span>
              <span v-else>
                已选择 {{ selectedColumns.length }} 列进行批量操作
              </span>
            </div>
          </div>

          <!-- 列选择区域 -->
          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
            <div
              v-for="column in visibleColumns"
              :key="column"
              class="column-item flex items-center p-2 bg-white border rounded-lg hover:shadow-sm transition-all cursor-pointer"
              :class="getColumnItemClass(column)"
              @click="toggleColumnVisibility(column)"
            >
              <div class="flex items-center flex-1 min-w-0">
                <i 
                  :class="getColumnIconClass(column)"
                  class="mr-2 flex-shrink-0"
                ></i>
                <span 
                  class="text-sm truncate"
                  :class="getColumnTextClass(column)"
                  :title="columnMapping[column]?.LABEL || column"
                >
                  {{ columnMapping[column]?.LABEL || column }}
                </span>
              </div>
              <div class="ml-2 flex-shrink-0">
                <i 
                  :class="getColumnStatusIconClass(column)"
                  class="text-xs"
                ></i>
              </div>
            </div>
          </div>

          <!-- 操作按钮区域 -->
          <div v-if="!isBatchMode" class="flex items-center justify-between mt-4 pt-3 border-t border-gray-200">
            <div class="flex items-center space-x-2">
              <button
                @click="showAllColumns"
                class="px-3 py-1.5 text-xs bg-indigo-50 text-indigo-600 border border-indigo-200 rounded-md hover:bg-indigo-100 transition-colors"
                :disabled="hiddenColumns.length === 0"
              >
                <i class="fas fa-eye mr-1"></i>
                显示所有列
              </button>
              <button
                @click="hideAllColumns"
                class="px-3 py-1.5 text-xs bg-gray-50 text-gray-600 border border-gray-200 rounded-md hover:bg-gray-100 transition-colors"
                :disabled="hiddenColumns.length === visibleColumns.length"
              >
                <i class="fas fa-eye-slash mr-1"></i>
                隐藏所有列
              </button>
            </div>
            <button
              @click="showColumnConfig = false"
              class="px-3 py-1.5 text-xs bg-gray-100 text-gray-600 rounded-md hover:bg-gray-200 transition-colors"
            >
              完成
            </button>
          </div>
          
          <!-- 批量模式操作提示 -->
          <div v-else class="mt-4 pt-3 border-t border-gray-200">
            <div class="text-center text-sm text-gray-600">
              <i class="fas fa-info-circle mr-1"></i>
              点击列名进行选择，然后使用上方按钮批量显示或隐藏
            </div>
          </div>
        </div>

        <!-- 表格 -->
        <div class="table-container overflow-auto flex-1">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th
                  v-for="column in visibleColumns"
                  :key="column"
                  class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sticky top-0 bg-gray-50 border-b border-r border-gray-200 relative group"
                  :style="{ width: getColumnWidth(column), minWidth: '50px' }"
                  :title="columnMapping[column]?.LABEL || column"
                >
                  <div class="whitespace-nowrap pr-4">
                    {{ columnMapping[column]?.LABEL || column }}
                  </div>
                  <!-- 列宽调整手柄 -->
                  <div
                    class="absolute top-0 right-0 w-1 h-full cursor-col-resize opacity-0 hover:opacity-100 transition-all duration-200 group-hover:opacity-60"
                    @mousedown="startResize(column, $event)"
                    :class="{ 'opacity-100 bg-indigo-500': resizingColumn === column }"
                    title="拖拽调整列宽"
                    style="z-index: 20;"
                  >
                    <div class="w-full h-full bg-indigo-400 hover:bg-indigo-500 transition-colors"></div>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <!-- 没有分页数据的情况 -->
              <tr v-if="paginatedRows.length === 0">
                <td
                  :colspan="visibleColumns.length || 1"
                  class="px-4 py-4 text-center text-sm text-gray-500"
                >
                  <span v-if="isLoading">加载数据中...</span>
                  <span v-else>
                    没有符合条件的数据 (paginatedRows长度: {{ paginatedRows.length }}, visibleColumns长度: {{ visibleColumns.length }})
                    <button
                      @click="debugDataStructure"
                      class="ml-2 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded text-gray-700"
                    >
                      查看数据结构
                    </button>
                  </span>
                </td>
              </tr>

              <!-- 正常行数据渲染 -->
              <tr v-for="(row, rowIndex) in paginatedRows" :key="rowIndex" class="hover:bg-gray-50">
                <td
                  v-for="column in visibleColumns"
                  :key="column"
                  class="px-2 py-2 text-sm text-gray-700 whitespace-nowrap overflow-hidden text-ellipsis"
                  :title="formatCellValue(getCellValue(row, column))"
                  :style="{ width: getColumnWidth(column), minWidth: '50px' }"
                >
                  {{ formatCellValue(getCellValue(row, column)) }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <div class="px-4 py-2 bg-white border-t flex justify-between items-center">
          <div class="flex items-center text-sm text-gray-600">
            <span>每页显示</span>
            <select
              v-model="pageSize"
              @change="(e: Event) => updatePageSize(Number((e.target as HTMLSelectElement).value))"
              class="mx-1 px-2 py-1 border rounded text-sm"
              :disabled="isPaginationLoading"
            >
              <option v-for="size in pageSizeOptions" :key="size" :value="size">{{ size }}</option>
            </select>
            <span>条</span>

            <!-- 分页加载状态 -->
            <span v-if="isPaginationLoading" class="ml-2 text-blue-600">
              <i class="fas fa-spinner fa-spin"></i>
              加载中...
            </span>
          </div>

          <div class="flex items-center space-x-1">
            <button
              @click="goToPage(1)"
              :disabled="currentPage === 1 || isPaginationLoading"
              class="p-2 rounded hover:bg-gray-100 text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              <i class="fas fa-angle-double-left"></i>
            </button>
            <button
              @click="goToPage(currentPage - 1)"
              :disabled="currentPage === 1 || isPaginationLoading"
              class="p-2 rounded hover:bg-gray-100 text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              <i class="fas fa-angle-left"></i>
            </button>

            <span class="text-sm text-gray-600">
              {{ currentPage }}
            </span>

            <button
              @click="goToPage(currentPage + 1)"
              :disabled="isPaginationLoading"
              class="p-2 rounded hover:bg-gray-100 text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              <i class="fas fa-angle-right"></i>
            </button>
            <button
              @click="goToPage(currentPage + 1)"
              :disabled="isPaginationLoading"
              class="p-2 rounded hover:bg-gray-100 text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              <i class="fas fa-angle-double-right"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- 可视化视图 -->
      <div v-else-if="activeView === 'visualization'" class="flex-1 overflow-auto">
        <QueryVisualization
          :query-id="queryId || ''"
          :query-result="results"
          :is-loading="isLoading"
        />
      </div>

      <!-- 分析视图 -->
      <div v-else-if="activeView === 'analysis'" class="flex-1 overflow-auto">
        <QueryAnalysis
          :query-id="queryId || ''"
          :data-source-id="results?.dataSourceId || ''"
          :parameters="results?.parameters || {}"
          :result-id="results?.id || ''"
          :sql="results?.sql || ''"
          @apply-suggestion="handleApplySuggestion"
        />
      </div>

      <!-- 执行计划视图 -->
      <div v-else-if="activeView === 'execution-plan'" class="flex-1 overflow-auto">
        <QueryExecutionPlan
          :query-id="queryId || ''"
          :data-source-id="dataSourceId || ''"
          :sql="sql || ''"
          :query-type="queryType || 'sql'"
          :parameters="parameters || {}"
          :schema-id="schemaId || ''"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.table-container {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* 表格响应式优化 */
.table-container {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* 表格列宽优化 */
table {
  table-layout: fixed;
  width: 100%;
}

/* 单元格内容优化 */
td, th {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 大屏幕优化 - 充分利用屏幕宽度 */
@media (min-width: 1200px) {
  .table-container {
    overflow-x: visible;
  }
  
  table {
    width: 100%;
  }
}

/* 列头优化 - 确保列名完整显示 */
th {
  vertical-align: top;
  white-space: nowrap;
  overflow: visible;
  text-overflow: unset;
  position: relative;
}

/* 列分隔线样式 - 类似DBeaver */
th::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background-color: #d1d5db;
  z-index: 1;
}

/* 最后一列不显示分隔线 */
th:last-child::after {
  display: none;
}

/* 列头文本样式 */
th div {
  line-height: 1.2;
  overflow: visible;
  text-overflow: unset;
}

/* 拖拽调整列宽样式 - 类似DBeaver的优雅设计 */
.cursor-col-resize {
  cursor: col-resize;
}

.cursor-col-resize:hover {
  background-color: #6366f1 !important;
}

/* 拖拽时的视觉反馈 */
.resizing {
  user-select: none;
}

/* 列宽调整手柄样式 - 微妙优雅 */
th .resize-handle {
  position: absolute;
  top: 0;
  right: 0;
  width: 2px;
  height: 100%;
  background-color: transparent;
  cursor: col-resize;
  transition: all 0.2s ease;
  opacity: 0;
}

th .resize-handle:hover {
  background-color: #6366f1;
  opacity: 1;
}

th .resize-handle.active {
  background-color: #4f46e5;
  opacity: 1;
}

/* 列头悬停时显示手柄 */
th:hover .resize-handle {
  opacity: 0.6;
  background-color: #9ca3af;
}

/* 列管理面板动画 */
.rotate-180 {
  transform: rotate(180deg);
  transition: transform 0.2s ease;
}

/* 列选择项动画 */
.column-item {
  transition: all 0.2s ease;
}

.column-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
