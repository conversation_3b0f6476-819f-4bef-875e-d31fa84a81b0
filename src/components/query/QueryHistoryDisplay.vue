<template>
  <div class="query-history-display">
    <!-- 操作按钮 -->
    <div v-if="queryHistoryRecords.length > 0" class="flex justify-end items-center mb-1">
      <div class="flex space-x-2">
        <button
          @click="toggleExpanded"
          class="text-sm text-gray-600 hover:text-gray-800 px-2 py-1 rounded hover:bg-gray-100"
        >
          {{ isExpanded ? '收起' : '展开' }}
        </button>
        <button
          @click="clearHistory"
          class="text-sm text-red-600 hover:text-red-800 px-2 py-1 rounded hover:bg-red-50"
        >
          清除历史
        </button>
      </div>
    </div>

    <!-- 历史记录列表 -->
    <div v-if="queryHistoryRecords.length > 0" class="space-y-1 max-h-80 overflow-y-auto">
      <div
        v-for="(record, index) in queryHistoryRecords"
        :key="record.id"
        class="border rounded-lg overflow-hidden border-green-300 bg-green-50"
        :class="{ 'max-h-12': !isExpanded && !expandedRecords.has(record.id) }"
      >
        <!-- 记录头部 -->
        <div
          class="px-3 py-2 border-b cursor-pointer bg-green-100 border-green-200"
          @click="toggleRecordExpanded(record.id)"
        >
          <div class="flex justify-between items-center">
            <div class="flex items-center space-x-2">
              <span class="text-sm font-medium text-green-800">
                历史查询
              </span>
              <span class="text-xs px-2 py-1 rounded bg-green-200 text-green-700">
                {{ formatExecutionTime(record.executionTime) }}
              </span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-xs text-gray-500">
                {{ formatDateTime(record.executedAt) }}
              </span>
              <i
                class="fas transition-transform cursor-pointer hover:text-gray-700"
                :class="{
                  'fa-chevron-down': !expandedRecords.has(record.id),
                  'fa-chevron-up': expandedRecords.has(record.id)
                }"
                @click.stop="toggleRecordExpanded(record.id)"
              ></i>
            </div>
          </div>
        </div>

        <!-- 记录内容 -->
        <div
          v-if="expandedRecords.has(record.id)"
          class="p-3 space-y-3"
        >
          <!-- SQL语句 -->
          <div>
            <h4 class="text-sm font-medium text-gray-700 mb-2">SQL语句</h4>
            <div class="bg-gray-900 text-green-400 p-3 rounded text-sm font-mono overflow-x-auto border-l-4 border-green-400">
              <pre>{{ record.sql }}</pre>
            </div>
          </div>

          <!-- 查询结果 -->
          <div v-if="record.result">
            <h4 class="text-sm font-medium text-gray-700 mb-2">
              查询结果 
              <span class="text-xs text-gray-500 font-normal">
                (共 {{ record.result.rows?.length || 0 }} 行)
              </span>
            </h4>
            <div class="bg-white border rounded p-3 overflow-auto">
              <div v-if="record.result.rows && record.result.rows.length > 0" class="space-y-2">
                
                <!-- 结果表格 -->
                <div class="overflow-x-auto">
                  <table class="min-w-full text-sm">
                    <thead class="bg-gray-50">
                      <tr>
                        <th
                          v-for="field in record.result.fields"
                          :key="field.name"
                          class="px-3 py-2 text-left font-medium text-gray-700 border-b"
                        >
                          {{ field.label || field.name }}
                        </th>
                      </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                      <tr
                        v-for="(row, rowIndex) in record.result.rows"
                        :key="rowIndex"
                        class="hover:bg-gray-50"
                      >
                        <td
                          v-for="field in record.result.fields"
                          :key="field.name"
                          class="px-3 py-2 text-gray-900 border-b"
                        >
                          {{ row[field.name] }}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                
                <!-- 更多数据提示 -->
                <div v-if="record.result.rows.length > 10" class="text-sm text-gray-500 mt-2">
                  仅显示前10行，共 {{ record.result.rows.length }} 行数据
                </div>
              </div>
              <div v-else class="text-gray-500 text-sm">
                无数据
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex justify-end items-center text-sm">
            <button
              @click="removeRecord(record.id)"
              class="text-red-600 hover:text-red-800 text-xs"
            >
              删除
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="text-center py-8 text-gray-500">
      <i class="fas fa-history text-4xl mb-2"></i>
      <p>暂无查询历史</p>
      <p class="text-sm">执行多次查询后将显示历史记录</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { useQueryStore } from '@/stores/query'
import type { QueryHistoryRecord } from '@/stores/query'

// 定义组件属性
const props = defineProps<{
  maxRecords?: number // 最大显示记录数
}>()

// 使用查询存储
const queryStore = useQueryStore()

// 响应式数据
const isExpanded = ref(false) // 默认收起
const expandedRecords = ref(new Set<string>())

// 计算属性
const queryHistoryRecords = computed(() => {
  const records = queryStore.queryHistoryRecords
  // 过滤掉最近一次查询（isLatest为true的记录）
  const filteredRecords = records.filter(record => !record.isLatest)
  // 按照执行时间倒序排序（最新的在上面）
  const sortedRecords = filteredRecords.sort((a, b) => new Date(b.executedAt).getTime() - new Date(a.executedAt).getTime())
  return props.maxRecords ? sortedRecords.slice(0, props.maxRecords) : sortedRecords
})

// 方法
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
  if (!isExpanded.value) {
    expandedRecords.value.clear()
  } else {
    // 展开时默认展开第一条记录
    if (queryHistoryRecords.value.length > 0) {
      expandedRecords.value.add(queryHistoryRecords.value[0].id)
    }
  }
}

const toggleRecordExpanded = (recordId: string) => {
  if (expandedRecords.value.has(recordId)) {
    expandedRecords.value.delete(recordId)
  } else {
    expandedRecords.value.add(recordId)
  }
}

const clearHistory = () => {
  if (confirm('确定要清除所有查询历史吗？')) {
    queryStore.clearQueryHistoryRecords()
  }
}

const removeRecord = (recordId: string) => {
  if (confirm('确定要删除这条查询历史吗？')) {
    queryStore.removeQueryHistoryRecord(recordId)
  }
}

const formatExecutionTime = (time: number) => {
  if (time < 1000) {
    return `${time}ms`
  } else {
    return `${(time / 1000).toFixed(2)}s`
  }
}

const formatDateTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'COMPLETED': '成功',
    'FAILED': '失败',
    'RUNNING': '执行中',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}
</script>

<style scoped>
.query-history-display {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 自定义滚动条 */
.overflow-x-auto::-webkit-scrollbar {
  height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
</style>
