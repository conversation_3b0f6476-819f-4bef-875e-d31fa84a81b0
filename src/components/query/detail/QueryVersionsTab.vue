<template>
  <div>
    <div v-if="isLoading" class="py-12 flex justify-center">
      <div class="flex flex-col items-center">
        <div class="w-10 h-10 border-2 border-indigo-600 border-t-transparent rounded-full animate-spin mb-3"></div>
        <span class="text-gray-600">加载版本数据...</span>
      </div>
    </div>

    <div v-if="!isLoading && versions.length === 0" class="py-12 text-center">
      <i class="fas fa-code-branch text-3xl text-gray-400 mb-3"></i>
      <h3 class="text-lg font-medium text-gray-900 mb-2">暂无版本记录</h3>
      <p class="text-gray-500 mb-4">此查询尚未创建任何版本</p>
    </div>

    <div v-else-if="!isLoading && versions.length > 0" class="versions-list">
      <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
        <table class="min-w-full divide-y divide-gray-300">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">版本</th>
              <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">状态</th>
              <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">服务状态</th>
              <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">创建时间</th>
              <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">发布时间</th>
              <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                <span class="sr-only">操作</span>
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200 bg-white">
            <tr
              v-for="version in versions"
              :key="version.id"
              :class="{
                'bg-blue-50': isCurrentVersion(version),
                'hover:bg-gray-50': !isCurrentVersion(version)
              }"
            >
              <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                <div class="flex items-center">
                  <span :class="{ 'font-medium': isCurrentVersion(version) }">
                    v{{ version.versionNumber }}
                  </span>
                </div>
              </td>
              <td class="whitespace-nowrap px-3 py-4 text-sm">
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="{
                    'bg-yellow-100 text-yellow-800': version.status === 'DRAFT',
                    'bg-green-100 text-green-800': version.status === 'PUBLISHED',
                    'bg-gray-100 text-gray-800': version.status === 'DEPRECATED'
                  }"
                >
                  {{ formatStatus(version.status) }}
                </span>
              </td>
              <td class="whitespace-nowrap px-3 py-4 text-sm">
                <span
                  v-if="version.isActive"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  <i class="fas fa-check-circle mr-1"></i>
                  已启用
                </span>
                <span
                  v-else-if="version.status === 'PUBLISHED'"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                >
                  已禁用
                </span>
                <span v-else class="text-gray-500">-</span>
              </td>
              <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                {{ formatDate(version.createdAt) }}
              </td>
              <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                {{ version.publishedAt ? formatDate(version.publishedAt) : '—' }}
              </td>
              <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                <div class="flex justify-end space-x-2">
                  <button
                    @click="viewVersion(version)"
                    class="text-indigo-600 hover:text-indigo-900"
                    title="查看版本详情"
                  >
                    <i class="fas fa-eye"></i>
                  </button>
                  <button
                    v-if="version.status === 'DRAFT'"
                    @click="publishVersion(version.id)"
                    class="text-green-600 hover:text-green-900"
                    title="发布版本"
                  >
                    <i class="fas fa-check"></i>
                  </button>
                  <button
                    v-if="version.status === 'PUBLISHED' && !isCurrentVersion(version)"
                    @click="activateVersion(queryId, version.id)"
                    class="text-blue-600 hover:text-blue-900"
                    title="激活版本"
                  >
                    <i class="fas fa-play"></i>
                  </button>
                  <button
                    v-if="version.status === 'PUBLISHED'"
                    @click="deprecateVersion(version.id)"
                    class="text-red-600 hover:text-red-900"
                    title="废弃版本"
                  >
                    <i class="fas fa-archive"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div v-if="totalPages > 1" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
          <button
            @click="currentPage > 1 && changePage(currentPage - 1)"
            :disabled="currentPage === 1"
            class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }"
          >
            上一页
          </button>
          <button
            @click="currentPage < totalPages && changePage(currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }"
          >
            下一页
          </button>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              显示第 <span class="font-medium">{{ startItem }}</span> 到第 <span class="font-medium">{{ endItem }}</span> 条，共 <span class="font-medium">{{ totalItems }}</span> 条结果
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                @click="currentPage > 1 && changePage(currentPage - 1)"
                :disabled="currentPage === 1"
                class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }"
              >
                <span class="sr-only">上一页</span>
                <i class="fas fa-chevron-left h-5 w-5"></i>
              </button>

              <template v-for="page in displayPages" :key="typeof page === 'number' ? page : `ellipsis-${page}`">
                <button
                  v-if="page !== '...'"
                  @click="changePage(page)"
                  class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
                  :class="{ 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600': page === currentPage }"
                >
                  {{ page }}
                </button>
                <span v-else class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                  ...
                </span>
              </template>

              <button
                @click="currentPage < totalPages && changePage(currentPage + 1)"
                :disabled="currentPage === totalPages"
                class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }"
              >
                <span class="sr-only">下一页</span>
                <i class="fas fa-chevron-right h-5 w-5"></i>
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>

    <!-- 确认对话框 -->
    <a-modal
      v-model:visible="confirmDialogVisible"
      :title="confirmDialogTitle"
      @ok="handleConfirmAction"
      @cancel="cancelAction"
      :okText="confirmButtonText"
      okType="danger"
      cancelText="取消"
    >
      <p>{{ confirmDialogMessage }}</p>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import type { QueryVersion, QueryVersionStatus } from '@/types/queryVersion';
import { versionService } from '@/services/versionService';
import { useQueryStore } from '@/stores/query';
import type { Query } from '@/types/query';
import dayjs from 'dayjs';
import axios from 'axios';
import { getApiBasePath } from '@/utils/config';
import {getApiBaseUrl} from "@/services/query";
import instance from "@/utils/axios";

const props = defineProps<{
  queryId: string;
  activeVersionNumber?: number;
}>();

const router = useRouter();
const queryStore = useQueryStore();

// 开发模式标志
const isDev = false; // 禁用开发模式

// 状态变量
const isLoading = ref(true);
const versions = ref<QueryVersion[]>([]);
const totalItems = ref(0);
const totalPages = ref(1);
const currentPage = ref(1);
const pageSize = ref(10); // 每页显示10条记录

// 确认对话框状态
const confirmDialogVisible = ref(false);
const confirmDialogTitle = ref('');
const confirmDialogMessage = ref('');
const confirmButtonText = ref('');
const pendingAction = ref<(() => void) | null>(null);

// 格式化状态显示
const formatStatus = (status: QueryVersionStatus) => {
  const statusMap: Record<QueryVersionStatus, string> = {
    'DRAFT': '草稿',
    'PUBLISHED': '已发布',
    'DEPRECATED': '已废弃'
  };
  return statusMap[status] || status;
};

// 格式化日期显示
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 计算分页信息
const startItem = computed(() => (currentPage.value - 1) * pageSize.value + 1);
const endItem = computed(() => Math.min(currentPage.value * pageSize.value, totalItems.value));

// 计算显示的页码
const displayPages = computed(() => {
  const total = totalPages.value;
  const current = currentPage.value;

  if (total <= 7) {
    return Array.from({ length: total }, (_, i) => i + 1);
  }

  // 总是显示第一页和最后一页
  const result: Array<number | string> = [1];

  if (current <= 3) {
    // 靠近开始
    result.push(2, 3, 4, '...', total);
  } else if (current >= total - 2) {
    // 靠近结束
    result.push('...', total - 3, total - 2, total - 1, total);
  } else {
    // 中间
    result.push('...', current - 1, current, current + 1, '...', total);
  }

  return result;
});

// 格式化版本号，确保使用真实版本ID
const formatVersionNumber = (versionId: string | null): string => {
  if (!versionId) return '无版本';

  // 查找版本对象以获取版本号
  const version = versions.value.find(v => v.id === versionId);
  if (version && version.versionNumber) {
    return version.versionNumber.toString();
  }

  // 如果找不到对应的版本对象，返回默认值
  return '1';
}

// 判断版本是否为活跃版本
const isActiveVersion = (versionId: string | null): boolean => {
  if (!versionId || !props.activeVersionNumber) return false;
  return versionId === String(props.activeVersionNumber);
};

// 如果版本列表为空，显示一个提示消息
const hasNoVersions = computed(() => {
  return !isLoading.value && (!versions.value || versions.value.length === 0);
});

// 检查当前查询是否已包含版本信息
const hasExistingVersionData = computed(() => {
  const query = queryStore.currentQuery;
  return query && query.currentVersion;
});

// 加载版本数据
const loadVersions = async () => {
  isLoading.value = true;

  try {
    console.log('加载版本数据，查询ID:', props.queryId, '页码:', currentPage.value, '每页大小:', pageSize.value);

    // 强制使用正确的API路径请求版本列表数据，无论是否有缓存数据
    console.log('强制使用 /api/queries/{id}/versions 获取版本列表');

    // 构建查询参数
    const queryParams = new URLSearchParams();
    queryParams.append('page', currentPage.value.toString());
    queryParams.append('size', pageSize.value.toString());

    // 确保使用完整的API路径
    const apiUrl = `/api/queries/${props.queryId}/versions?${queryParams.toString()}`;
    console.log('请求版本列表完整URL:', apiUrl);

    // 添加时间戳防止缓存
    const requestURL = `${apiUrl}&_t=${Date.now()}`;

    // 关键：使用axios直接请求，不使用任何封装的服务
    const response = await instance.get(requestURL);

    console.log('版本API原始响应:', response);

    // 处理响应数据
    let versionsData = [];

    if (response.data) {
      // 处理响应数据结构 - 尝试多种可能的格式
      if (Array.isArray(response.data)) {
        versionsData = response.data;
      } else if (response.data.data && Array.isArray(response.data.data)) {
        versionsData = response.data.data;
      } else if (response.data.data && response.data.data.items && Array.isArray(response.data.data.items)) {
        versionsData = response.data.data.items;
        totalItems.value = response.data.data.total || versionsData.length;
        totalPages.value = response.data.data.totalPages || Math.ceil(totalItems.value / pageSize.value);
      } else if (response.data.items && Array.isArray(response.data.items)) {
        versionsData = response.data.items;
        totalItems.value = response.data.total || versionsData.length;
        totalPages.value = response.data.totalPages || Math.ceil(totalItems.value / pageSize.value);
      }

      if (versionsData.length > 0) {
        // 将API返回的数据格式标准化为组件需要的格式
        versions.value = versionsData.map((item: any) => ({
          id: item.id || `version-${item.versionNumber}`,
          queryId: props.queryId,
          versionNumber: item.versionNumber || 1,
          queryText: item.sql || '',
          status: item.status || 'PUBLISHED',
          isActive: item.isLatest === true,
          createdAt: item.createdAt || new Date().toISOString(),
          updatedAt: item.updatedAt || new Date().toISOString(),
          publishedAt: item.publishedAt,
          dataSourceId: item.dataSourceId
        }));

        console.log('处理后的版本数据:', versions.value);
      } else {
        console.warn('API返回的版本数据为空');
        versions.value = [];
      }
    }

    if (!totalItems.value) {
      totalItems.value = versions.value.length;
    }
    if (!totalPages.value) {
      totalPages.value = Math.ceil(totalItems.value / pageSize.value);
    }
  } catch (error) {
    console.error('加载版本数据失败:', error);
    message.error('无法加载版本数据，请稍后重试');
    versions.value = [];
    totalItems.value = 0;
    totalPages.value = 1;
  } finally {
    isLoading.value = false;
  }
};

// 切换页码
const changePage = async (page: number | string) => {
  currentPage.value = typeof page === 'number' ? page : parseInt(page);
  // 页码变化后重新加载数据
  await loadVersions();
};

// 监听页码变化
watch(currentPage, () => {
  // 只有当组件已挂载且没有使用现有版本数据时才重新加载
  if (!hasExistingVersionData.value) {
    loadVersions();
  }
}, { immediate: false });

// 组件挂载时加载数据
onMounted(() => {
  loadVersions();
});

// 创建新版本
const createNewVersion = () => {
  router.push(`/query/edit/${props.queryId}/new-version`);
};

// 查看版本
const viewVersion = (version: QueryVersion) => {
  // 添加调试信息
  console.log('查看版本:', version);

  try {
    // 使用命名路由进行导航
    router.push({
      name: 'QueryVersionDetails',
      params: {
        id: props.queryId,
        versionId: version.id
      }
    });

    // 通知用户
    message.info(`正在查看版本 V${version.versionNumber} 的详情`);
  } catch (error) {
    console.error('路由跳转失败:', error);
    message.error(`路由跳转失败: ${error instanceof Error ? error.message : String(error)}`);

    // 使用备选方案，采用完整的URL路径
    const baseUrl = window.location.origin;
    window.location.href = `${baseUrl}/query/detail/${props.queryId}/version/${version.id}`;
  }
};

// 发布版本
const publishVersion = async (versionId: string) => {
  try {
    console.log('开始发布版本流程:', versionId);

    // 先调用前置接口检查
    const isExposedInYop = await versionService.checkPublishBeforehand(props.queryId, versionId);
    console.log('前置检查结果 - 是否已在yop网关暴露:', isExposedInYop);

    // 根据前置检查结果设置不同的确认信息
    if (isExposedInYop) {
      confirmDialogTitle.value = '发布确认';
      confirmDialogMessage.value = '此查询关联的集成配置已在yop网关暴露，修改可能会对此产生影响，是否继续发布？';
      confirmButtonText.value = '继续发布';
    } else {
      confirmDialogTitle.value = '发布版本';
      confirmDialogMessage.value = '确定要发布此版本吗？发布后将无法修改，只能废弃。';
      confirmButtonText.value = '发布';
    }

    confirmDialogVisible.value = true;

    pendingAction.value = async () => {
      try {
        console.log('正在发布版本:', versionId);

        // 使用versionService发布版本
        const response = await versionService.publishVersion(versionId);

        if (response) {
          await loadVersions();
          message.success('版本已成功发布');
        } else {
          throw new Error('发布版本失败');
        }
      } catch (error) {
        console.error('发布版本失败:', error);
        message.error('发布版本失败，请稍后重试');
      }
    };
  } catch (error) {
    console.error('发布前置检查失败:', error);
    // 如果前置检查失败，使用默认的发布流程
    confirmDialogTitle.value = '发布版本';
    confirmDialogMessage.value = '确定要发布此版本吗？发布后将无法修改，只能废弃。';
    confirmButtonText.value = '发布';
    confirmDialogVisible.value = true;

    pendingAction.value = async () => {
      try {
        console.log('正在发布版本:', versionId);

        // 使用versionService发布版本
        const response = await versionService.publishVersion(versionId);

        if (response) {
          await loadVersions();
          message.success('版本已成功发布');
        } else {
          throw new Error('发布版本失败');
        }
      } catch (error) {
        console.error('发布版本失败:', error);
        message.error('发布版本失败，请稍后重试');
      }
    };
  }
};

// 废弃版本
const deprecateVersion = (versionId: string) => {
  confirmDialogTitle.value = '废弃版本';
  confirmDialogMessage.value = '确定要废弃此版本吗？废弃后将无法恢复。';
  confirmButtonText.value = '废弃';
  confirmDialogVisible.value = true;

  pendingAction.value = async () => {
    try {
      console.log('正在废弃版本:', versionId);

      // 使用versionService废弃版本
      const response = await versionService.deprecateVersion(versionId);

      if (response) {
        await loadVersions();
        message.success('版本已成功废弃');
      } else {
        throw new Error('废弃版本失败');
      }
    } catch (error) {
      console.error('废弃版本失败:', error);
      message.error('废弃版本失败，请稍后重试');
    }
  };
};

// 激活版本
const activateVersion = async (queryId: string, versionId: string) => {
  confirmDialogTitle.value = '激活版本';
  confirmDialogMessage.value = '确定要将此版本设为活跃版本吗？';
  confirmButtonText.value = '激活';
  confirmDialogVisible.value = true;

  pendingAction.value = async () => {
    try {
      console.log('正在激活版本:', queryId, versionId);

      // 使用versionService激活版本
      const response = await versionService.activateVersion(queryId, versionId);

      console.log('激活版本API响应:', response);
              if (response) {
          // 重新加载版本列表以获取最新状态
          await loadVersions();
          message.success('版本已成功激活');
        } else {
          throw new Error('激活版本失败');
        }
    } catch (error) {
      console.error('激活版本失败:', error);
      message.error('激活版本失败，请稍后重试');
    }
  };
};

// 处理确认对话框的确认操作
const handleConfirmAction = async () => {
  if (pendingAction.value) {
    try {
      await pendingAction.value();
    } catch (error) {
      console.error('操作执行失败:', error);
      message.error('操作执行失败，请稍后重试');
    } finally {
      confirmDialogVisible.value = false;
    }
  }
};

// 取消确认对话框
const cancelAction = () => {
  confirmDialogVisible.value = false;
  pendingAction.value = null;
  message.success('取消成功');
};

// 处理版本数据映射
const processVersions = (data: any[]) => {
  versions.value = data.map((v: any) => ({
    ...v,
    isActive: v.id === (props.activeVersionNumber ? String(props.activeVersionNumber) : null)
  }));
  totalItems.value = data.length;
  totalPages.value = Math.ceil(totalItems.value / pageSize.value);
};

// 判断是否为当前版本
const isCurrentVersion = (version: QueryVersion): boolean => {
  return version.isActive === true;
};
</script>

<style scoped>
.versions-tab-container {
  padding: 1.5rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* 添加过渡效果 */
tr {
  @apply transition-colors duration-150 ease-in-out;
}

/* 操作按钮样式 */
button {
  @apply p-1.5 rounded-full transition-colors duration-150 ease-in-out;
}

button:hover {
  @apply bg-gray-100;
}
</style>
