<template>
  <div class="h-full flex flex-col bg-white">
    <!-- 执行计划头部 -->
    <div class="px-4 py-3 bg-gray-50 border-b">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <i class="fas fa-project-diagram text-indigo-500 mr-2"></i>
          <h3 class="text-lg font-medium text-gray-900">查询执行计划</h3>
        </div>
        <div class="flex items-center space-x-2">
          <button
            @click="loadExecutionPlan"
            :disabled="isLoading"
            class="px-3 py-1 text-sm bg-indigo-600 text-white rounded hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <i class="fas fa-sync-alt mr-1" :class="{'fa-spin': isLoading}"></i>
            刷新
          </button>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="flex-1 flex items-center justify-center">
      <div class="text-center text-gray-500">
        <div class="w-8 h-8 border-2 border-gray-300 border-t-indigo-500 rounded-full animate-spin mx-auto mb-2"></div>
        <p>正在获取执行计划...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="flex-1 flex items-center justify-center">
      <div class="text-center text-red-500">
        <i class="fas fa-exclamation-triangle text-3xl mb-2"></i>
        <p class="text-lg font-medium">获取执行计划失败</p>
        <p class="text-sm mt-1">{{ error }}</p>
        <button
          @click="loadExecutionPlan"
          class="mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          重试
        </button>
      </div>
    </div>

    <!-- 执行计划内容 -->
    <div v-else-if="executionPlan" class="flex-1 overflow-auto p-4">
      <!-- 执行计划统计信息 -->
      <div class="mb-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-blue-50 p-4 rounded-lg">
          <div class="flex items-center">
            <i class="fas fa-clock text-blue-500 mr-2"></i>
          <div>
              <p class="text-sm text-blue-600">规划时间</p>
              <p class="text-lg font-semibold text-blue-900">{{ executionPlan.planningTime || '-' }}ms</p>
            </div>
          </div>
        </div>
        <div class="bg-green-50 p-4 rounded-lg">
          <div class="flex items-center">
            <i class="fas fa-chart-line text-green-500 mr-2"></i>
          <div>
              <p class="text-sm text-green-600">预估行数</p>
              <p class="text-lg font-semibold text-green-900">{{ formatNumber(executionPlan.estimatedRows) }}</p>
            </div>
          </div>
        </div>
        <div class="bg-purple-50 p-4 rounded-lg">
                  <div class="flex items-center">
            <i class="fas fa-calculator text-purple-500 mr-2"></i>
            <div>
              <p class="text-sm text-purple-600">预估成本</p>
              <p class="text-lg font-semibold text-purple-900">{{ executionPlan.estimatedCost || '-' }}</p>
                  </div>
                </div>
              </div>
        <div class="bg-orange-50 p-4 rounded-lg">
          <div class="flex items-center">
            <i class="fas fa-database text-orange-500 mr-2"></i>
            <div>
              <p class="text-sm text-orange-600">数据库类型</p>
              <p class="text-lg font-semibold text-orange-900">{{ databaseType }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 执行计划表格 -->
      <div class="bg-white border rounded-lg overflow-hidden">
        <div class="px-4 py-3 bg-gray-50 border-b">
          <h4 class="text-md font-medium text-gray-900">执行计划详情</h4>
        </div>
        
        <div class="overflow-x-auto max-h-96 overflow-y-auto">
          <table class="w-full divide-y divide-gray-200" style="table-layout: auto; min-width: max-content;">
            <thead class="bg-gray-50">
              <tr>
                <!-- TiDB格式表头 -->
                <template v-if="isTiDBFormat">
                  <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 60px;">
                    <i class="fas fa-sort-numeric-down mr-1"></i>
                    id
                  </th>
                  <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 100px;">
                    <i class="fas fa-sort-numeric-down mr-1"></i>
                    estRows
                  </th>
                  <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 120px;">
                    <i class="fas fa-tasks mr-1"></i>
                    task
                  </th>
                  <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 150px;">
                    <i class="fas fa-database mr-1"></i>
                    access object
                  </th>
                  <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 150px;">
                    <i class="fas fa-info-circle mr-1"></i>
                    operator info
                  </th>
                </template>
                <!-- MySQL格式表头 -->
                <template v-else>
                  <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 60px;">
                    <i class="fas fa-sort-numeric-down mr-1"></i>
                    id
                  </th>
                  <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 120px;">
                    <i class="fas fa-sort-alpha-down mr-1"></i>
                    select_type
                  </th>
                  <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 150px;">
                    <i class="fas fa-table mr-1"></i>
                    table
                  </th>
                  <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 100px;">
                    <i class="fas fa-layer-group mr-1"></i>
                    partitions
                  </th>
                  <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 80px;">
                    <i class="fas fa-sort-alpha-down mr-1"></i>
                    type
                  </th>
                  <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 150px;">
                    <i class="fas fa-key mr-1"></i>
                    possible_keys
                  </th>
                  <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 120px;">
                    <i class="fas fa-key mr-1"></i>
                    key
                  </th>
                  <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 80px;">
                    <i class="fas fa-sort-numeric-down mr-1"></i>
                    key_len
                  </th>
                  <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 80px;">
                    <i class="fas fa-link mr-1"></i>
                    ref
                  </th>
                  <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 80px;">
                    <i class="fas fa-sort-numeric-down mr-1"></i>
                    rows
                  </th>
                  <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 100px;">
                    <i class="fas fa-percentage mr-1"></i>
                    filtered
                  </th>
                  <th class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="min-width: 150px;">
                    <i class="fas fa-info-circle mr-1"></i>
                    Extra
                  </th>
                </template>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="(step, index) in parsedPlanSteps" :key="index" class="hover:bg-gray-50">
                <!-- TiDB格式数据行 -->
                <template v-if="isTiDBFormat">
                  <td class="px-2 py-3 text-sm text-gray-900 font-mono whitespace-nowrap">
                    <span v-if="step.indent" class="text-gray-400 font-mono">{{ step.indent }}</span>{{ step.id || '-' }}
                  </td>
                  <td class="px-2 py-3 text-sm text-gray-900 whitespace-nowrap">
                    {{ step.rows || '-' }}
                  </td>
                  <td class="px-2 py-3 text-sm text-gray-900 whitespace-nowrap">
                    {{ step.selectType || '-' }}
                  </td>
                  <td class="px-2 py-3 text-sm text-gray-900 font-mono whitespace-nowrap">
                    {{ step.table || '-' }}
                  </td>
                  <td class="px-2 py-3 text-sm text-gray-900 whitespace-nowrap">
                    <span v-if="step.extra" class="text-xs bg-gray-100 px-2 py-1 rounded">
                      {{ step.extra }}
                    </span>
                    <span v-else>-</span>
                  </td>
                </template>
                <!-- MySQL格式数据行 -->
                <template v-else>
                  <td class="px-2 py-3 text-sm text-gray-900 font-mono whitespace-nowrap">
                    {{ step.id || '-' }}
                  </td>
                  <td class="px-2 py-3 text-sm text-gray-900 whitespace-nowrap">
                    {{ step.selectType || '-' }}
                  </td>
                  <td class="px-2 py-3 text-sm text-gray-900 font-mono whitespace-nowrap">
                    {{ step.table || '-' }}
                  </td>
                  <td class="px-2 py-3 text-sm text-gray-900 whitespace-nowrap">
                    {{ step.partitions || '-' }}
                  </td>
                  <td class="px-2 py-3 text-sm text-gray-900 whitespace-nowrap">
                    {{ step.type || '-' }}
                  </td>
                  <td class="px-2 py-3 text-sm text-gray-900 font-mono whitespace-nowrap">
                    {{ step.possibleKeys || '-' }}
                  </td>
                  <td class="px-2 py-3 text-sm text-gray-900 font-mono whitespace-nowrap">
                    {{ step.key || '-' }}
                  </td>
                  <td class="px-2 py-3 text-sm text-gray-900 whitespace-nowrap">
                    {{ step.keyLen || '-' }}
                  </td>
                  <td class="px-2 py-3 text-sm text-gray-900 whitespace-nowrap">
                    {{ step.ref || '-' }}
                  </td>
                  <td class="px-2 py-3 text-sm text-gray-900 whitespace-nowrap">
                    {{ step.rows || '-' }}
                  </td>
                  <td class="px-2 py-3 text-sm text-gray-900 whitespace-nowrap">
                    {{ step.filtered || '-' }}
                  </td>
                  <td class="px-2 py-3 text-sm text-gray-900 whitespace-nowrap">
                    <span v-if="step.extra" class="text-xs bg-gray-100 px-2 py-1 rounded">
                      {{ step.extra }}
                    </span>
                    <span v-else>-</span>
                  </td>
                </template>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 原始执行计划文本 -->
      <div class="mt-6 bg-white border rounded-lg overflow-hidden">
        <div class="px-4 py-3 bg-gray-50 border-b">
          <h4 class="text-md font-medium text-gray-900">原始执行计划</h4>
        </div>
        <div class="p-4">
          <pre class="text-sm text-gray-700 whitespace-pre font-mono bg-gray-50 p-4 rounded overflow-x-auto" style="max-width: 100%; word-wrap: break-word;">{{ executionPlan.plan }}</pre>
        </div>
      </div>
    </div>

    <!-- 无执行计划状态 -->
    <div v-else class="flex-1 flex items-center justify-center">
      <div class="text-center text-gray-500">
        <i class="fas fa-project-diagram text-3xl mb-2"></i>
        <p class="text-lg font-medium">暂无执行计划</p>
        <p class="text-sm mt-1">点击刷新按钮获取执行计划</p>
        <button
            @click="loadExecutionPlan"
          class="mt-3 px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
        >
          获取执行计划
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import { queryService } from '@/services/query'
import { useQueryStore } from '@/stores/query'

// 定义组件属性
const props = defineProps<{
  queryId?: string
  dataSourceId?: string
  sql?: string
  queryType?: string
  parameters?: Record<string, any>
  schemaId?: string
}>()

// 获取store
const queryStore = useQueryStore()

// 状态
const isLoading = ref(false)
const error = ref<string | null>(null)
const executionPlan = ref<any>(null)

// 计算属性：数据库类型
const databaseType = computed(() => {
  // 根据数据源ID或其他信息判断数据库类型
  // 这里可以根据实际情况进行判断
  return 'MySQL/DB2'
})

// 计算属性：判断是否为TiDB格式
const isTiDBFormat = computed(() => {
  if (!executionPlan.value?.plan) return false
  return isTreeFormat(executionPlan.value.plan)
})

// 计算属性：解析后的执行计划步骤
const parsedPlanSteps = computed(() => {
  if (!executionPlan.value?.plan) return []

  try {
    const planText = executionPlan.value.plan
    console.log('执行计划原始文本:', planText)
    
    // 智能识别执行计划格式
    if (isTreeFormat(planText)) {
      return parseTreeFormat(planText)
    } else if (isTableFormat(planText)) {
      return parseTableFormat(planText)
    } else {
      // 尝试通用解析
      return parseGenericFormat(planText)
    }
  } catch (err) {
    console.error('解析执行计划失败:', err)
    return []
  }
})

// 判断是否为树形格式（TiDB等）
const isTreeFormat = (planText) => {
  return planText.includes('├─') || planText.includes('└─') || planText.includes('│')
}

// 判断是否为表格格式（MySQL等）
const isTableFormat = (planText) => {
  const lines = planText.split('\n').filter(line => line.trim())
  if (lines.length < 2) return false
  
  const firstLine = lines[0]
  return firstLine.includes('id\t') && (firstLine.includes('select_type\t') || firstLine.includes('testRows\t'))
}

// 解析树形格式（TiDB等）
const parseTreeFormat = (planText) => {
  console.log('解析树形格式执行计划')
  const lines = planText.split('\n').filter(line => line.trim())
  const steps = []
  
  for (const line of lines) {
    // 跳过标题行
    if (line.includes('id\ttestRows\ttask\taccess object\toperator info')) {
      continue
    }
    
    // 解析树形结构中的每一行
    // 格式：├─TableReader_197    560.00    root            MppVersion: 2, data:ExchangeSender_196
    // 或者：PartitionUnion_191    133418266.00    root
    let cleanLine = line.trim()
    if (!cleanLine) continue
    
    // 处理树形符号，保留缩进信息
    const treeSymbols = line.match(/^([├└│\s]*)/)?.[1] || ''
    const hasTreeSymbols = treeSymbols.includes('├') || treeSymbols.includes('└') || treeSymbols.includes('│')
    
    if (hasTreeSymbols) {
      // 移除树形符号但保留缩进
      cleanLine = line.replace(/^[├└│\s]+/, '').trim()
    }
    
    const parts = cleanLine.split('\t')
    if (parts.length >= 5) {
      // TiDB格式：id, testRows, task, access object, operator info
      const step = {
        id: parts[0]?.trim() || '',
        rows: parts[1]?.trim() || '',
        selectType: parts[2]?.trim() || '', // task
        table: extractTableName(parts[3]?.trim() || ''),
        partitions: extractPartition(parts[3]?.trim() || ''),
        type: extractType(parts[0]?.trim() || ''),
        possibleKeys: '',
        key: '',
        keyLen: '',
        ref: '',
        filtered: '100.0',
        extra: parts[4]?.trim() || '',
        // 添加缩进信息用于显示
        indent: hasTreeSymbols ? treeSymbols : ''
      }
      steps.push(step)
    } else if (parts.length >= 3) {
      // 处理只有3列的情况（如PartitionUnion_191）
      const step = {
        id: parts[0]?.trim() || '',
        rows: parts[1]?.trim() || '',
        selectType: parts[2]?.trim() || '', // task
        table: '',
        partitions: '',
        type: extractType(parts[0]?.trim() || ''),
        possibleKeys: '',
        key: '',
        keyLen: '',
        ref: '',
        filtered: '100.0',
        extra: '',
        indent: hasTreeSymbols ? treeSymbols : ''
      }
      steps.push(step)
    }
  }
  
  return steps
}

// 解析表格格式（MySQL等）
const parseTableFormat = (planText) => {
  console.log('解析表格格式执行计划')
  const lines = planText.split('\n').filter(line => line.trim())
  const steps = []
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]
    
    // 跳过标题行
    if (line.includes('id\t') && (line.includes('select_type\t') || line.includes('testRows\t'))) {
      continue
    }
    
    const parts = line.split('\t')
    if (parts.length >= 12) {
      // MySQL格式：id, select_type, table, partitions, type, possible_keys, key, key_len, ref, rows, filtered, Extra
      const step = {
        id: parts[0]?.trim() || '',
        selectType: parts[1]?.trim() || '',
        table: parts[2]?.trim() || '',
        partitions: parts[3]?.trim() || '',
        type: parts[4]?.trim() || '',
        possibleKeys: parts[5]?.trim() || '',
        key: parts[6]?.trim() || '',
        keyLen: parts[7]?.trim() || '',
        ref: parts[8]?.trim() || '',
        rows: parts[9]?.trim() || '',
        filtered: parts[10]?.trim() || '',
        extra: parts[11]?.trim() || ''
      }
      steps.push(step)
    } else if (parts.length >= 5) {
      // TiDB表格格式：id, testRows, task, access object, operator info
      const step = {
        id: parts[0]?.trim() || '',
        selectType: 'SIMPLE',
        table: parts[3]?.trim() || '',
        partitions: '',
        type: 'ALL',
        possibleKeys: '',
        key: '',
        keyLen: '',
        ref: '',
        rows: parts[1]?.trim() || '',
        filtered: '100.0',
        extra: parts[4]?.trim() || ''
      }
      steps.push(step)
    }
  }
  
  return steps
}

// 通用解析格式
const parseGenericFormat = (planText) => {
  console.log('使用通用解析格式')
  const lines = planText.split('\n').filter(line => line.trim())
  const steps = []
  
  for (const line of lines) {
    // 尝试按制表符分割
    const parts = line.split('\t')
    if (parts.length >= 3) {
      const step = {
        id: parts[0]?.trim() || '',
        selectType: parts[1]?.trim() || 'SIMPLE',
        table: parts[2]?.trim() || '',
        partitions: parts[3]?.trim() || '',
        type: parts[4]?.trim() || 'ALL',
        possibleKeys: parts[5]?.trim() || '',
        key: parts[6]?.trim() || '',
        keyLen: parts[7]?.trim() || '',
        ref: parts[8]?.trim() || '',
        rows: parts[9]?.trim() || parts[1]?.trim() || '',
        filtered: parts[10]?.trim() || '100.0',
        extra: parts[11]?.trim() || parts[4]?.trim() || ''
      }
      steps.push(step)
    }
  }
  
  return steps
}

// 从access object中提取表名
const extractTableName = (accessObject) => {
  if (!accessObject) return ''
  const match = accessObject.match(/table:([^,]+)/)
  return match ? match[1] : ''
}

// 从access object中提取分区信息
const extractPartition = (accessObject) => {
  if (!accessObject) return ''
  const match = accessObject.match(/partition:([^,]+)/)
  return match ? match[1] : ''
}

// 从操作符名称中提取类型
const extractType = (operatorName) => {
  if (!operatorName) return 'ALL'
  if (operatorName.includes('TableReader')) return 'ALL'
  if (operatorName.includes('TableFullScan')) return 'ALL'
  if (operatorName.includes('IndexReader')) return 'ref'
  if (operatorName.includes('IndexLookUp')) return 'ref'
  if (operatorName.includes('HashJoin')) return 'ALL'
  if (operatorName.includes('Sort')) return 'ALL'
  return 'ALL'
}


// 格式化数字
const formatNumber = (num: number | null | undefined): string => {
  if (num === null || num === undefined) {
    return '-'
  }
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 获取操作类型样式类
const getOperationTypeClass = (operationType: string) => {
  const type = operationType.toLowerCase()
  if (type.includes('scan')) {
    return 'bg-blue-100 text-blue-800'
  } else if (type.includes('index')) {
    return 'bg-green-100 text-green-800'
  } else if (type.includes('join')) {
    return 'bg-purple-100 text-purple-800'
  } else if (type.includes('sort')) {
    return 'bg-yellow-100 text-yellow-800'
  } else if (type.includes('group')) {
    return 'bg-red-100 text-red-800'
  }
  return 'bg-gray-100 text-gray-800'
}

// 加载执行计划
const loadExecutionPlan = async () => {
  if (!props.dataSourceId || !props.sql) {
    error.value = '缺少必要参数：数据源ID或SQL语句'
    return
  }

  isLoading.value = true
  error.value = null

  try {

    // 调用执行计划API
    const result = await queryService.getExecutionPlan({
      dataSourceId: props.dataSourceId,
      sql: props.sql,
      queryType: props.queryType || 'sql',
      parameters: props.parameters || {},
      schemaId: props.schemaId
    })

    if (result) {
      executionPlan.value = result
    } else {
      error.value = '执行计划数据为空'
    }
  } catch (err: any) {
    console.error('获取执行计划失败:', err)
    error.value = err.message || '获取执行计划失败'
    message.error('获取执行计划失败: ' + (err.message || '未知错误'))
  } finally {
    isLoading.value = false
  }
}

// 监听props变化，当参数完整时自动加载执行计划
watch(() => [props.dataSourceId, props.sql], ([newDataSourceId, newSql]) => {
  if (newDataSourceId && newSql) {
    loadExecutionPlan()
  }
}, { immediate: true })
</script>

<style scoped>
/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* 表格样式 */
table {
  border-collapse: collapse;
}

th, td {
  border: 1px solid #e5e7eb;
}

/* 代码块样式 */
pre {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  line-height: 1.4;
}

/* 表格响应式优化 */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* 表格列宽优化 */
table {
  table-layout: auto;
  min-width: max-content;
}

/* 单元格内容优化 */
td, th {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
  .table-responsive {
    overflow-x: visible;
  }
  
  table {
    width: 100%;
  }
}
</style>