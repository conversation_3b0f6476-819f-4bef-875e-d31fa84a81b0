<template>
  <div class="execution-history-view">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">查询日志</h1>
      <p class="page-description">查看和管理SQL查询的执行历史记录</p>
    </div>

    <!-- 查询条件区域 -->
    <div class="search-section">
      <div class="search-form">
        <div class="form-row">
          <!-- 数据源选择 -->
          <div class="form-group">
            <label class="form-label">数据源</label>
            <a-select
              v-model:value="searchParams.dataSourceName"
              placeholder="请选择数据源"
              allow-clear
              style="width: 200px"
              @change="handleDataSourceChange"
            >
              <a-select-option
                v-for="dataSource in dataSourceOptions"
                :key="dataSource.value"
                :value="dataSource.value"
              >
                {{ dataSource.label }}
              </a-select-option>
            </a-select>
          </div>

          <!-- Schema输入 -->
          <div class="form-group">
            <label class="form-label">Schema</label>
            <a-input
              v-model:value="searchParams.schemaName"
              placeholder="请输入Schema"
              allow-clear
              style="width: 200px"
            />
          </div>

          <!-- 执行人 -->
          <div class="form-group">
            <label class="form-label">执行人</label>
            <a-input
              v-model:value="searchParams.executedBy"
              placeholder="请输入执行人"
              allow-clear
              style="width: 200px"
            />
          </div>

          <!-- 执行时间范围 -->
          <div class="form-group">
            <label class="form-label">执行时间</label>
            <a-range-picker
              v-model:value="dateRange"
              format="YYYY-MM-DD"
              placeholder="['开始日期', '结束日期']"
              style="width: 400px"
              :get-popup-container="getPopupContainer"
              @change="handleDateRangeChange"
            />
          </div>

          <!-- 执行状态 -->
          <div class="form-group">
            <label class="form-label">执行状态</label>
            <a-select
              v-model:value="searchParams.status"
              placeholder="请选择状态"
              allow-clear
              style="width: 150px"
            >
              <a-select-option
                v-for="(config, status) in EXECUTION_STATUS_CONFIG"
                :key="status"
                :value="status"
              >
                {{ config.label }}
              </a-select-option>
            </a-select>
          </div>

          <!-- 查询按钮 -->
          <div class="form-actions">
            <a-button type="primary" @click="handleSearch" :loading="loading">
              <template #icon>
                <SearchOutlined />
              </template>
              查询
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <div class="table-header">
        <div class="table-title">
          <span>执行历史记录</span>
          <a-badge :count="pagination.total" :number-style="{ backgroundColor: '#52c41a' }" />
        </div>
        <div class="table-actions">
          <a-button @click="handleRefresh" :loading="loading">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </div>
      </div>

      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="false"
        row-key="id"
        :scroll="{ x: 1200 }"
        size="middle"
      >
        <!-- 执行状态列 -->
        <template #status="{ record }">
          <a-tag :color="getStatusColor(record.status)">
            <template #icon>
              <component :is="getStatusIcon(record.status)" />
            </template>
            {{ getStatusLabel(record.status) }}
          </a-tag>
        </template>

        <!-- 查询类型列 -->
        <template #logType="{ record }">
          <a-tag :color="getLogSourceColor(record.logType)">
            {{ getLogSourceLabel(record.logType) }}
          </a-tag>
        </template>

        <!-- 执行时间列 -->
        <template #executedAt="{ record }">
          <div class="time-cell">
            <div class="time-main">{{ formatDateTime(record.executedAt) }}</div>
            <div class="time-duration">耗时: {{ record.duration }}ms</div>
          </div>
        </template>

        <!-- 执行SQL列 -->
        <template #executeSql="{ record }">
          <div class="sql-cell">
            <div class="sql-content" :title="record.executeSql">
              {{ truncateText(record.executeSql, 100) }}
            </div>
          </div>
        </template>


        <!-- 操作列 -->
        <template #actions="{ record }">
          <a-space>
            <a-button 
              type="link" 
              size="small" 
              @click="handleViewDetail(record)"
            >
              查看详情
            </a-button>
          </a-space>
        </template>
      </a-table>

      <!-- 分页组件 -->
      <div class="pagination-wrapper">
        <a-pagination
          v-model:current="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
          :page-size-options="['10', '20', '50', '100']"
          @change="handlePageChange"
          @show-size-change="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 详情弹窗 -->
    <ExecutionDetailModal
      v-model:visible="detailModalVisible"
      :execution-data="selectedExecution"
      @re-execute="handleReExecute"
    />

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { message, Modal } from 'ant-design-vue';
import {
  SearchOutlined,
  ReloadOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  LoadingOutlined,
  MinusCircleOutlined
} from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import ExecutionDetailModal from './components/ExecutionDetailModal.vue';
import { executionHistoryService } from '@/services/executionHistoryService';
import { dataSourceService } from '@/services/datasource';
import { EXECUTION_STATUS_CONFIG, LOG_SOURCE_CONFIG } from '@/types/execution-history';
import type {
  ExecutionHistory,
  ExecutionHistoryQueryParams
} from '@/types/execution-history';

// 响应式数据
const loading = ref(false);
const dataSource = ref<ExecutionHistory[]>([]);
const dateRange = ref<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
const detailModalVisible = ref(false);
const selectedExecution = ref<ExecutionHistory | null>(null);
const dataSourceOptions = ref<Array<{label: string, value: string}>>([]);

// 搜索参数
const searchParams = reactive<ExecutionHistoryQueryParams>({
  page: 1,
  size: 20,
  dataSourceName: '',
  schemaName: '',
  executedBy: '',
  status: ''
});

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
  totalPages: 0
});

// 表格列配置
const columns = [
  {
    title: '执行时间',
    dataIndex: 'executedAt',
    key: 'executedAt',
    width: 180,
    slots: { customRender: 'executedAt' }
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    slots: { customRender: 'status' }
  },
  {
    title: '查询类型',
    dataIndex: 'logType',
    key: 'logType',
    width: 120,
    slots: { customRender: 'logType' }
  },
  {
    title: '数据源/Schema',
    dataIndex: 'dbSchemaName',
    key: 'dbSchemaName',
    width: 150,
    customRender: ({ record }: { record: ExecutionHistory }) => {
      return record.dbSchemaName || '-';
    }
  },
  {
    title: '执行人',
    dataIndex: 'executedBy',
    key: 'executedBy',
    width: 120,
    customRender: ({ record }: { record: ExecutionHistory }) => {
      return record.executedBy || '-';
    }
  },
  {
    title: '执行SQL',
    dataIndex: 'executeSql',
    key: 'executeSql',
    width: 300,
    slots: { customRender: 'executeSql' }
  },
  {
    title: '行数',
    dataIndex: 'rowCount',
    key: 'rowCount',
    width: 80,
    align: 'center' as const
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right' as const,
    slots: { customRender: 'actions' }
  }
];


// 获取执行历史列表
const fetchExecutionHistory = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchParams,
      page: pagination.page,
      size: pagination.size
    };
    
    console.log('[查询日志] 请求参数:', params);
    
    const response = await executionHistoryService.getExecutionHistory(params);
    
    console.log('[查询日志] API响应:', response);
    
    if (response.success) {
      dataSource.value = response.data.items;
      pagination.total = response.data.total;
      pagination.totalPages = response.data.totalPages;
    } else {
      message.error(response.message || '获取执行历史失败');
    }
  } catch (error: any) {
    console.error('获取执行历史失败:', error);
    message.error(error.message || '获取执行历史失败');
  } finally {
    loading.value = false;
  }
};

// 处理数据源变更
const handleDataSourceChange = (dataSourceName: string) => {
  searchParams.dataSourceName = dataSourceName;
};

// 加载数据源选项
const loadDataSourceOptions = async () => {
  try {
    console.log('[查询日志] 开始获取数据源列表');
    const response = await dataSourceService.getDataSources({ page: 1, size: 1000 });
    const dataSources = response.items || [];
    
    console.log('[查询日志] 获取到数据源列表:', dataSources);
    
    dataSourceOptions.value = dataSources.map(ds => ({
      label: ds.name,
      value: ds.name
    }));
    
    console.log('[查询日志] 数据源选项:', dataSourceOptions.value);
  } catch (error) {
    console.error('加载数据源选项失败:', error);
    // 如果API调用失败，使用默认选项
    dataSourceOptions.value = [
      { label: 'tidb', value: 'tidb' },
      { label: 'mysql', value: 'mysql' },
      { label: 'postgresql', value: 'postgresql' },
      { label: 'oracle', value: 'oracle' }
    ];
  }
};

// 处理日期范围变更
const handleDateRangeChange = (dates: [dayjs.Dayjs, dayjs.Dayjs] | null) => {
  if (dates && dates.length === 2) {
    // 只传递年月日，不包含时分秒
    searchParams.executedAtStart = dates[0].format('YYYY-MM-DD');
    searchParams.executedAtEnd = dates[1].format('YYYY-MM-DD');
  } else {
    searchParams.executedAtStart = '';
    searchParams.executedAtEnd = '';
  }
};

// 获取弹窗容器，确保日期选择器不被覆盖
const getPopupContainer = (trigger: HTMLElement) => {
  return trigger.parentElement || document.body;
};

// 处理查询
const handleSearch = () => {
  pagination.page = 1;
  fetchExecutionHistory();
};

// 处理重置
const handleReset = () => {
  Object.assign(searchParams, {
    page: 1,
    size: 20,
    dataSourceName: '',
    schemaName: '',
    executedBy: '',
    status: '',
    executedAtStart: '',
    executedAtEnd: ''
  });
  dateRange.value = null;
  pagination.page = 1;
  fetchExecutionHistory();
};

// 处理刷新
const handleRefresh = () => {
  fetchExecutionHistory();
};

// 处理分页变更
const handlePageChange = (page: number) => {
  pagination.page = page;
  fetchExecutionHistory();
};

// 处理页面大小变更
const handlePageSizeChange = (current: number, size: number) => {
  pagination.page = 1;
  pagination.size = size;
  fetchExecutionHistory();
};

// 处理查看详情
const handleViewDetail = (record: ExecutionHistory) => {
  selectedExecution.value = record;
  detailModalVisible.value = true;
};


// 处理重新执行
const handleReExecute = async (record: ExecutionHistory) => {
  try {
    await executionHistoryService.reExecuteQuery(record.id);
    message.success('重新执行成功');
    fetchExecutionHistory();
  } catch (error: any) {
    message.error(error.message || '重新执行失败');
  }
};

// 工具函数
const getStatusColor = (status: string) => {
  return EXECUTION_STATUS_CONFIG[status as keyof typeof EXECUTION_STATUS_CONFIG]?.color || 'default';
};

const getStatusLabel = (status: string) => {
  return EXECUTION_STATUS_CONFIG[status as keyof typeof EXECUTION_STATUS_CONFIG]?.label || status;
};

const getStatusIcon = (status: string) => {
  const iconMap = {
    success: CheckCircleOutlined,
    error: CloseCircleOutlined,
    running: LoadingOutlined,
    cancelled: MinusCircleOutlined
  };
  return iconMap[status as keyof typeof iconMap] || CheckCircleOutlined;
};

// 日志来源相关函数
const getLogSourceLabel = (logSource?: string) => {
  if (!logSource) return '-';
  return LOG_SOURCE_CONFIG[logSource as keyof typeof LOG_SOURCE_CONFIG]?.label || logSource;
};

const getLogSourceColor = (logSource?: string) => {
  if (!logSource) return 'default';
  return LOG_SOURCE_CONFIG[logSource as keyof typeof LOG_SOURCE_CONFIG]?.color || 'default';
};

const formatDateTime = (dateTime: string) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss');
};

const truncateText = (text: string, maxLength: number) => {
  if (!text) return '';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
};

// 监听分页参数变化
watch([() => pagination.page, () => pagination.size], () => {
  fetchExecutionHistory();
});

// 组件挂载时加载数据
onMounted(() => {
  loadDataSourceOptions();
  fetchExecutionHistory();
});
</script>

<style scoped>
.execution-history-view {
  padding: 20px;
  background: #f8fafc;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
  padding: 20px 0;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-description {
  color: #64748b;
  margin: 0;
  font-size: 16px;
}

.search-section {
  background: white;
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.form-row {
  display: flex;
  gap: 20px;
  align-items: end;
  flex-wrap: wrap;
  justify-content: space-between;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 180px;
}

.form-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.form-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-left: auto;
}

.table-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 700;
  color: #1a202c;
}

.table-actions {
  display: flex;
  gap: 12px;
}

.time-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.time-main {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.time-duration {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
  width: fit-content;
}

.sql-cell {
  max-width: 300px;
}

.sql-content {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 12px;
  color: #374151;
  line-height: 1.5;
  word-break: break-all;
  background: #f8fafc;
  padding: 8px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}


.pagination-wrapper {
  padding: 20px 24px;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: center;
  background: #f8fafc;
}

/* 确保日期选择器弹窗不被覆盖 */
:deep(.ant-picker-dropdown) {
  z-index: 9999 !important;
}

:deep(.ant-picker-panel-container) {
  z-index: 9999 !important;
}

/* 美化表格样式 */
:deep(.ant-table) {
  border-radius: 0;
}

:deep(.ant-table-thead > tr > th) {
  background: #f8fafc;
  border-bottom: 2px solid #e2e8f0;
  font-weight: 600;
  color: #374151;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f8fafc;
}

:deep(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid #f1f5f9;
}

/* 美化按钮样式 */
:deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
}

:deep(.ant-btn-primary:hover) {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  box-shadow: 0 6px 8px -1px rgba(102, 126, 234, 0.4);
}

/* 美化输入框样式 */
:deep(.ant-input),
:deep(.ant-select-selector) {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.2s ease;
}

:deep(.ant-input:focus),
:deep(.ant-select-focused .ant-select-selector) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 美化标签样式 */
:deep(.ant-tag) {
  border-radius: 6px;
  font-weight: 500;
  border: none;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .form-row {
    gap: 16px;
  }
  
  .form-group {
    min-width: 160px;
  }
}

@media (max-width: 768px) {
  .execution-history-view {
    padding: 16px;
  }
  
  .form-row {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .form-group {
    min-width: auto;
  }
  
  .form-actions {
    margin-left: 0;
    justify-content: center;
  }
  
  .table-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .table-actions {
    justify-content: center;
  }
  
  .page-title {
    font-size: 24px;
  }
}
</style>
