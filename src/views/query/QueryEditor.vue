<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import QueryEditorPage from './QueryEditorPage.vue';

/**
 * 查询编辑器路由组件
 *
 * 该组件是查询编辑器的入口点，负责：
 * 1. 从路由参数中提取查询相关信息
 * 2. 将这些参数传递给QueryEditorPage主容器组件
 * 3. 通过计算属性确保类型安全和默认值处理
 */

const route = useRoute();

// 从路由参数中获取属性，并使用计算属性确保类型安全
const queryId = computed(() => route.params.id as string || route.query.id as string || undefined);
const initialDataSourceId = computed(() => route.query.dataSourceId as string || '');
const initialSql = computed(() => route.query.sql as string || '');
const initialQueryType = computed(() => route.query.queryType as string || 'SQL');
</script>

<template>
  <QueryEditorPage
    :queryId="queryId"
    :initialDataSourceId="initialDataSourceId"
    :initialSql="initialSql"
    :initialQueryType="initialQueryType"
  />
</template>
