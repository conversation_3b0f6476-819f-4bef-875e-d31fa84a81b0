import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import { useQueryStore } from '@/stores/query';
import type { QueryType } from '@/types/query';

/**
 * 查询执行可组合函数
 * 提供查询执行、取消、状态管理等功能
 */
export function useQueryExecution() {
  // 状态
  const isComponentMounted = ref(true);
  const isExecuting = ref(false);
  const executionTime = ref(0);
  const executionTimer = ref<number | null>(null);
  const queryError = ref<string | null>(null);
  const statusMessage = ref<string | null>(null);
  const queryResults = ref<any>(null);
  const currentQueryId = ref<string | null>(null);
  
  // 获取store
  const queryStore = useQueryStore();
  
  // 初始化查询执行状态
  const initializeQueryExecution = () => {
    // 重置错误状态
    queryError.value = null;
    statusMessage.value = null;
    isExecuting.value = true;
    
    // 重置并启动执行时间计时器
    executionTime.value = 0;
    if (executionTimer.value) {
      clearInterval(executionTimer.value);
    }
    executionTimer.value = window.setInterval(() => {
      executionTime.value += 1;
    }, 1000);
  };
  
  // 完成查询执行
  const finalizeQueryExecution = () => {
    // 清除执行时间计时器
    if (executionTimer.value) {
      clearInterval(executionTimer.value);
      executionTimer.value = null;
    }
    isExecuting.value = false;
    
    // 5秒后清除状态消息
    if (statusMessage.value) {
      setTimeout(() => {
        if (isComponentMounted.value) {
          statusMessage.value = null;
        }
      }, 5000);
    }
  };
  
  // 处理查询错误
  const handleQueryError = (error: any) => {
    let errorMsg;
    
    if (error?.response?.data?.error) {
      errorMsg = error.response.data.error;
    } else if (error?.message) {
      errorMsg = error.message;
    } else if (typeof error === 'string') {
      errorMsg = error;
    } else {
      errorMsg = '未知错误';
    }
    
    // 增强错误提示信息
    if (errorMsg.includes('bad SQL grammar')) {
      // 提取SQL语法错误的具体部分
      const match = errorMsg.match(/bad SQL grammar \[(.*?)\]/);
      if (match && match[1]) {
        const badSql = match[1];
        // 检查是否包含未替换的${param}占位符
        const paramPlaceholders = badSql.match(/\${([^}]+)}/g);
        if (paramPlaceholders && paramPlaceholders.length > 0) {
          const unprocessedParams = paramPlaceholders.map((p: string) => p.substring(2, p.length - 1)).join(', ');
          errorMsg = `SQL参数替换失败：未能正确替换参数 [${unprocessedParams}]。请检查参数值是否正确。`;
        } else {
          errorMsg = `SQL语法错误：${badSql}`;
        }
      } else {
        errorMsg = `SQL语法错误，请检查查询语句是否正确`;
      }
    } else if (errorMsg.includes('Table') && errorMsg.includes('doesn\'t exist')) {
      // 表不存在错误
      const tableMatch = errorMsg.match(/Table '(.+?)' doesn't exist/);
      if (tableMatch && tableMatch[1]) {
        errorMsg = `表 '${tableMatch[1]}' 不存在，请检查表名是否正确`;
      }
    }
    
    queryError.value = errorMsg;
    statusMessage.value = `执行失败：${errorMsg}`;
    
    // 显示错误提示
    message.error(errorMsg);
  };
  
  // 执行查询
  const executeQuery = async (params: {
    dataSourceId: string;
    queryText: string;
    queryType: QueryType;
    versionId?: string;
    schemaId?: string; // 新增：schema ID参数
  }) => {
    // 添加备用方案，尝试从DOM获取SQL内容，避免空查询问题
    if (params.queryType === 'SQL' && (!params.queryText || params.queryText.trim() === '')) {
      console.warn('useQueryExecution: queryText为空，尝试从编辑器DOM获取内容');
      try {
        const sqlEditor = document.querySelector('[data-cy="sql-editor"]');
        if (sqlEditor) {
          const editorText = sqlEditor.textContent || '';
          if (editorText.trim()) {
            console.log('成功从DOM获取SQL内容，长度：', editorText.length);
            params.queryText = editorText;
          }
        }
      } catch (e) {
        console.error('尝试从DOM获取SQL内容失败:', e);
      }
    }

    console.log('useQueryExecution:executeQuery 开始, 详细参数:', {
      dataSourceId: params.dataSourceId,
      queryType: params.queryType,
      queryText: params.queryText,
      queryTextLength: params.queryText?.length || 0,
      queryTextSample: params.queryText?.substring(0, 50),
      versionId: params.versionId
    });
    
    // 验证参数并显示明确的错误信息
    if (!params.dataSourceId) {
      const msg = '请先选择数据源';
      message.warning(msg);
      console.error('执行查询失败:', msg);
      queryError.value = msg;
      statusMessage.value = msg;
      return;
    }
    
    if (!params.queryText || params.queryText.trim() === '') {
      console.error('查询内容为空 - params对象内容:', JSON.stringify(params));
      let msg = '';
      if (params.queryType === 'SQL') {
        msg = 'SQL查询不能为空，请在编辑器中输入查询语句';
      } else if (params.queryType === 'natural-language') {
        msg = '自然语言查询不能为空，请输入您的问题';
      } else {
        msg = '查询内容不能为空';
      }
      
      message.warning(msg);
      console.error('执行查询失败:', msg);
      queryError.value = msg;
      statusMessage.value = msg;
      return;
    }
    
    // 初始化执行状态
    initializeQueryExecution();
    
    try {
      // 执行查询
      statusMessage.value = '正在执行查询...';
      console.log(`开始执行${params.queryType}查询...`);
      
      // 根据查询类型执行不同的查询
      let result;
      if (params.queryType === 'SQL') {
        console.log('执行SQL查询:', params.queryText.substring(0, 100) + (params.queryText.length > 100 ? '...' : ''));
        result = await queryStore.executeQuery({
          dataSourceId: params.dataSourceId,
          sql: params.queryText,
          queryType: 'SQL',
          versionId: params.versionId,
          parameters: {},
          page: 1, // 默认第一页
          size: 20, // 默认每页20条
          schemaId: params.schemaId // 传递schema ID
        });
      } else {
        console.log('执行自然语言查询:', params.queryText);
        result = await queryStore.executeNaturalLanguageQuery({
          dataSourceId: params.dataSourceId,
          question: params.queryText,
          versionId: params.versionId
        });
      }
      
      console.log('查询执行结果:', result);
      
      // 检查组件是否已卸载
      if (!isComponentMounted.value) {
        finalizeQueryExecution();
        return;
      }
      
      // 检查查询结果状态
      if (result) {
        const resultObj: any = ('query' in result && 'result' in result) ? result.result : result;
        if (resultObj.status === 'ERROR' || resultObj.error) {
          const errorMessage = resultObj.errorMessage || resultObj.error || '查询执行失败';
          throw new Error(errorMessage);
        }
      }
      
      // 更新查询ID - 优先从URL参数获取，然后从查询结果获取
      const urlQueryId = new URLSearchParams(window.location.hash.split('?')[1] || '').get('id');
      currentQueryId.value = urlQueryId || queryStore.currentQueryResult?.id || null;
      
      // 保存查询结果
      queryResults.value = result;
      
      // 更新状态信息
      const rowCount = queryStore.currentQueryResult?.rowCount || 0;
      const execTime = queryStore.currentQueryResult?.executionTime || 0;
      statusMessage.value = `查询执行成功，返回 ${rowCount} 条记录，耗时 ${execTime}ms`;
      message.success(statusMessage.value);
      console.log(statusMessage.value);
      
      // 注释掉自动获取执行计划的逻辑
      // try {
      //   await tryGetExecutionPlan(currentQueryId.value, {
      //     dataSourceId: params.dataSourceId,
      //     sql: params.queryText,
      //     queryType: params.queryType
      //   });
      // } catch (error) {
      //   console.warn('获取执行计划失败，但不影响查询结果显示:', error);
      // }
      
      return result;
    } catch (error) {
      handleQueryError(error);
      return null;
    } finally {
      finalizeQueryExecution();
    }
  };
  
  // 模拟延时函数
  const simulateDelay = (min: number, max: number) => {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min;
    return new Promise(resolve => setTimeout(resolve, delay));
  };
  
  // 取消查询
  const cancelQuery = async () => {
    if (!isExecuting.value) return;
    
    try {
      statusMessage.value = '正在取消查询...';
      
      // 模拟网络延迟
      await simulateDelay(800, 1500);
      
      // 如果有查询ID，尝试通过store取消
      if (currentQueryId.value) {
        await queryStore.cancelQuery(currentQueryId.value);
      }
      
      // 即使没有currentQueryId，也要强制取消当前执行状态
      isExecuting.value = false;
      
      // 清除执行时间计时器
      if (executionTimer.value) {
        clearInterval(executionTimer.value);
        executionTimer.value = null;
      }
      
      // 更新状态信息
      statusMessage.value = '查询已取消';
      
      // 添加模拟的错误消息
      queryError.value = '查询已被用户取消';
      
      setTimeout(() => {
        if (isComponentMounted.value) {
          statusMessage.value = null;
        }
      }, 3000);
    } catch (error) {
      console.error('取消查询失败:', error);
      statusMessage.value = '取消查询失败';
      
      // 即使取消失败，也要强制取消执行状态
      isExecuting.value = false;
      
      // 清除执行时间计时器
      if (executionTimer.value) {
        clearInterval(executionTimer.value);
        executionTimer.value = null;
      }
      
      setTimeout(() => {
        if (isComponentMounted.value) {
          statusMessage.value = null;
        }
      }, 5000);
    }
  };
  
  // 尝试获取执行计划，但允许失败
  const tryGetExecutionPlan = async (queryId: string | null, executionParams?: {
    dataSourceId: string;
    sql: string;
    queryType: QueryType;
  }) => {
    if (!isComponentMounted.value) return;

    try {
      // 等待一小段时间，确保后端有足够时间生成执行计划
      await new Promise(resolve => setTimeout(resolve, 500));

      // 检查是否是新建查询页面或临时ID
      // 更准确的判断：检查URL路径和查询参数
      const currentUrl = window.location.href;
      const urlParams = new URLSearchParams(window.location.hash.split('?')[1] || '');
      const urlQueryId = urlParams.get('id');

      const isNewQuery = window.location.pathname.includes('/query/new') ||
        window.location.pathname.includes('/query/create') ||
        currentUrl.includes('/query/editor') && !urlQueryId ||
        !queryId ||
        queryId.includes('temp-');

      console.log(`tryGetExecutionPlan: 检测是否为新增查询: ${isNewQuery}, queryId: ${queryId}, urlQueryId: ${urlQueryId}`);

      // 如果是新增查询，优先尝试使用SQL方式获取
      if (isNewQuery && executionParams && executionParams.sql && executionParams.dataSourceId) {
        console.log('检测到新增查询，直接使用SQL方式获取执行计划:', {
          sql: executionParams.sql.substring(0, 100) + (executionParams.sql.length > 100 ? '...' : ''),
          dataSourceId: executionParams.dataSourceId
        });

        try {
          await queryStore.getExecutionPlanForSql({
            dataSourceId: executionParams.dataSourceId,
            sql: executionParams.sql,
            parameters: {}
          });
          console.log('新增查询执行计划加载完成');
          return;
        } catch (sqlError) {
          console.error('使用SQL方式获取执行计划失败:', sqlError);
          // SQL方式失败，不再尝试ID方式，直接返回
          return;
        }
      }

      // 如果没有执行参数，尝试从store获取当前查询数据
      if (isNewQuery && !executionParams) {
        const currentQueryData = queryStore.currentQuery;
        if (currentQueryData && currentQueryData.queryText && currentQueryData.dataSourceId) {
          console.log('从store获取查询数据，使用SQL方式获取执行计划:', {
            sql: currentQueryData.queryText.substring(0, 100) + (currentQueryData.queryText.length > 100 ? '...' : ''),
            dataSourceId: currentQueryData.dataSourceId
          });

          try {
            await queryStore.getExecutionPlanForSql({
              dataSourceId: currentQueryData.dataSourceId,
              sql: currentQueryData.queryText,
              parameters: {}
            });
            console.log('新增查询执行计划加载完成');
            return;
          } catch (sqlError) {
            console.error('使用SQL方式获取执行计划失败:', sqlError);
            // SQL方式失败，不再尝试ID方式，直接返回
            return;
          }
        }
      }

      // 如果不是新增查询，或者没有足够的SQL信息，使用ID方式获取执行计划
      if (!isNewQuery && queryId) {
        console.log('使用ID方式获取执行计划:', queryId);

        // 尝试获取执行计划，最多重试2次
        let retries = 0;
        const maxRetries = 1;

        while (retries <= maxRetries) {
          try {
            await queryStore.getQueryExecutionPlan(queryId);
            console.log('执行计划加载完成');
            return;
          } catch (error: any) {
            // 检查错误是否是"执行计划不存在"或"查询不存在"
            const isNotFoundError =
              (error.message && (error.message.includes('计划不存在') || error.message.includes('查询不存在'))) ||
              (error.details && (error.details.includes('计划不存在') || error.details.includes('查询不存在')));

            if (isNotFoundError && retries < maxRetries) {
              console.log(`执行计划暂不可用，等待后重试 (${retries + 1}/${maxRetries})`);
              // 等待一段时间再重试
              await new Promise(resolve => setTimeout(resolve, 1000));
              retries++;
            } else {
              // 其他错误或已重试最大次数，停止尝试
              throw error;
            }
          }
        }
      }
    } catch (error) {
      console.error('获取执行计划失败:', error);
      // 不阻断主流程，仅记录错误
    }
  };
  
  // 组件卸载时清理资源
  const cleanup = () => {
    isComponentMounted.value = false;
    if (executionTimer.value) {
      clearInterval(executionTimer.value);
      executionTimer.value = null;
    }
  };
  
  return {
    // 状态
    isExecuting,
    executionTime,
    queryError,
    statusMessage,
    queryResults,
    currentQueryId,
    
    // 方法
    executeQuery,
    cancelQuery,
    cleanup,
    
    // 查询store提供访问
    queryStore
  };
}