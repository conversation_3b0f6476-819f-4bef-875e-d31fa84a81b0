<template>
  <div class="container mx-auto px-4 py-6" style="min-height: calc(100vh - 4rem);">
    <!-- 编辑器页头 -->
    <QueryEditorHeader
        :query-id="currentQueryId"
      :error-message="errorMessage"
      @clear-error="clearErrorMessage"
      @return-to-list="returnToList"
      @save-query="saveQuery"
      @publish-query="publishQuery"
    />

    <!-- 查询基本信息 -->
    <QueryBasicInfo
      v-model="queryName"
      :is-public="isPublic"
      :query-id="currentQueryId"
      :version-status="versionStatus"
      :is-active-version="isActiveVersion"
      :available-versions="availableVersions"
      v-model:selected-version="selectedVersion"
      :published-at="publishedAt"
      :last-edited-at="lastEditedAt"
      :deprecated-at="deprecatedAt"
      @update:isPublic="(value) => isPublic = value"
      @create-new-version="createNewVersion"
    />

    <!-- 内容区域：左侧数据源面板 + 右侧编辑器 -->
    <div class="grid gap-6" :class="isDataSourcePanelCollapsed ? 'grid-cols-1' : 'grid-cols-12'">
      <!-- 左侧元数据浏览面板 -->
      <div :class="isDataSourcePanelCollapsed ? 'hidden' : 'col-span-3'">
        <DataSourcePanel
          v-if="baseInfoLoaded"
          :data-source-id="selectedDataSourceId"
          :selected-schema="selectedSchema"
          :is-public="isPublic"
          :collapsed="isDataSourcePanelCollapsed"
          @data-source-change="handleDataSourceChange"
          @schema-change="handleSchemaSelection"
          @table-select="handleTableSelect"
          @column-select="handleColumnSelect"
          @insert-table="insertTableName"
          @insert-column="insertColumnName"
          @toggle-collapse="handleToggleDataSourcePanel"
        />
      </div>

      <!-- 右侧编辑器和结果区域 -->
      <div :class="isDataSourcePanelCollapsed ? 'col-span-1' : 'col-span-9'" class="space-y-6">
        <!-- 收起状态下的展开按钮 -->
        <div v-if="isDataSourcePanelCollapsed" class="flex items-center justify-between mb-4 p-3 bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg border border-indigo-200">
          <div class="flex items-center space-x-3">
            <button
              @click="handleToggleDataSourcePanel"
              class="flex items-center px-4 py-2 text-sm text-indigo-600 bg-white hover:bg-indigo-50 rounded-md border border-indigo-300 hover:border-indigo-400 transition-all duration-200 shadow-sm"
              title="展开数据源面板"
            >
              <i class="fas fa-chevron-right mr-2"></i>
              展开数据源面板
            </button>
            <div v-if="selectedDataSourceId" class="text-sm text-gray-600">
              <i class="fas fa-database mr-1.5 text-indigo-600"></i>
              {{ dataSourceStore.dataSources.find(ds => ds.id === selectedDataSourceId)?.name || '未知数据源' }}
              <span v-if="selectedSchema" class="ml-3">
                <i class="fas fa-schema mr-1 text-indigo-500"></i>
                {{ getSchemaDisplayName(selectedSchema) }}
              </span>
            </div>
          </div>
          <div class="text-xs text-gray-500 flex items-center">
            <i class="fas fa-info-circle mr-1"></i>
            收起状态 - 查询结果占用更多空间
          </div>
        </div>

        <!-- 查询编辑器标签页 -->
        <QueryEditorTabs
          v-model:activeTab="activeTab"
          :is-executing="isExecuting"
          :can-execute-sql="true"
          :can-execute-nlq="true"
          :can-execute-builder="true"
          @execute="handleExecuteFromTabs"
          @cancel="cancelQuery"
        />

        <!-- 查询编辑区域 -->
        <div class="bg-white shadow rounded-lg p-4">
          <!-- SQL编辑器 -->
          <div v-if="activeTab === 'editor'" class="min-h-[300px]">
            <div class="relative">
              <!-- 未保存更改提示 -->
              <div v-if="hasChanges" class="absolute right-2 top-2 p-1 rounded-md bg-yellow-50 border border-yellow-300 text-yellow-800 text-xs flex items-center">
                <i class="fas fa-exclamation-circle mr-1"></i>
                未保存更改
              </div>

              <SqlEditor
                ref="sqlEditorRef"
                v-model="sqlQuery"
                :data-source-id="selectedDataSourceId"
                @execute="executeQuery"
              />
            </div>

            <!-- 显示最后保存草稿时间和操作按钮区域 -->
            <div class="mt-4 flex justify-between">
              <div class="flex items-center">
                <span v-if="lastDraftSaveAt" class="ml-3 text-xs text-gray-500">
                  <i class="fas fa-history mr-1"></i>
                  草稿保存于: {{ formatLastSavedTime(lastDraftSaveAt) }}
                </span>
              </div>

              <div class="flex space-x-3">
              </div>
            </div>
          </div>

          <!-- 自然语言查询 -->
          <div v-else-if="activeTab === 'nlq'" class="h-full flex flex-col">
            <NaturalLanguageQuery
              v-model="naturalLanguageQuery"
              :data-source-id="selectedDataSourceId"
              @execute="executeQuery"
              @save="saveQuery"
            />

            <!-- 版本操作按钮区域 -->
            <div class="mt-4 flex justify-between">
              <span v-if="lastDraftSaveAt" class="text-xs text-gray-500 self-center">
                <i class="fas fa-history mr-1"></i>
                草稿保存于: {{ formatLastSavedTime(lastDraftSaveAt) }}
              </span>

              <div class="flex space-x-3">
              </div>
            </div>
          </div>

          <!-- 查询构建器 -->
          <div v-else-if="activeTab === 'builder'" class="h-full">
            <QueryManager
              ref="queryManagerRef"
              :current-query="builderQuery"
              :can-save="!!selectedDataSourceId && builderQuery.trim().length > 0"
              :query-state="builderState"
              @load="handleLoadQuery"
            />
            <QueryBuilder
              ref="queryBuilderRef"
              v-model="builderQuery"
              :data-source-id="selectedDataSourceId"
              @execute="executeBuilderQuery"
              @save="saveQuery"
              @update:state="updateBuilderState"
            />
          </div>
        </div>

        <!-- 错误消息提示 -->
        <transition name="fade">
          <div v-if="statusMessage && queryError"
              class="fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg border-l-4 max-w-md transform transition-all duration-300 bg-red-50 border-red-500 text-red-700"
          >
            <div class="flex items-center">
              <div class="flex-shrink-0 mr-3">
                <div class="w-8 h-8 rounded-full flex items-center justify-center bg-red-100">
                  <i class="fas fa-exclamation-circle text-lg text-red-500"></i>
                </div>
              </div>
              <div class="flex-1">
                <h3 class="text-sm font-semibold pb-0.5 text-red-800">错误提示</h3>
                <p class="text-sm">
                  {{ statusMessage }}
                </p>
              </div>
              <div class="ml-3">
                <button
                    @click="statusMessage = null"
                  class="inline-flex rounded-full p-1.5 text-red-500 hover:bg-red-100"
                >
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
          </div>
        </transition>

        <!-- 查询结果面板 -->
        <QueryResultPanel
          v-if="true"
          :results="queryStore.currentQueryResult"
          :is-loading="isExecuting"
          :error="queryError"
          :query-id="queryStore.currentQuery?.id"
          :is-visible="true"
          :is-natural-language-query="activeTab === 'nlq'"
          :data-source-id="selectedDataSourceId"
          :sql="currentExecutedSql || getQueryTextByType(activeTab === 'editor' ? 'SQL' : 'natural-language')"
          :query-type="activeTab === 'editor' ? 'sql' : (activeTab === 'nlq' ? 'natural-language' : 'sql')"
          :parameters="{}"
          :schema-id="selectedSchema"
          @export="(format: string) => exportResults(format as 'excel')"
          @page-change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 保存查询对话框 -->
    <SaveQueryModal
      v-if="isSaveModalVisible"
      :open="isSaveModalVisible"
      @update:open="isSaveModalVisible = $event"
      :query="{
        id: currentQueryId || '',
        name: queryName || '未命名查询',
        queryText: getQueryTextByType(activeTab === 'editor' ? 'SQL' : 'natural-language'),
        queryType: activeTab === 'builder' || activeTab === 'editor' ? 'SQL' : 'natural-language',
        dataSourceId: selectedDataSourceId
      }"
      :data-sources="dataSourceStore.dataSources || []"
      @save="handleSaveQuery"
    />

    <!-- 参数输入对话框 -->
    <QueryParamsDialog
      v-model:open="showParamsDialog"
      :parameters="queryParameters"
      @confirm="handleParamsConfirm"
      @cancel="handleParamsCancel"
    />
  </div>
</template>

<script setup lang="ts">
import {computed, nextTick, onMounted, onUnmounted, ref, watch} from 'vue';
import {useRoute, useRouter} from 'vue-router';
import {message} from 'ant-design-vue';

// 导入组件
import QueryEditorHeader from '@/components/query/editor/QueryEditorHeader.vue';
import QueryBasicInfo from '@/components/query/editor/QueryBasicInfo.vue';
import DataSourcePanel from '@/components/query/datasource/DataSourcePanel.vue';
import QueryEditorTabs from '@/components/query/editor/QueryEditorTabs.vue';
import SqlEditor from '@/components/query/SqlEditor.vue';
import NaturalLanguageQuery from '@/components/query/NaturalLanguageQuery.vue';
import QueryBuilder from '@/components/query/QueryBuilder.vue';
import QueryManager from '@/components/query/QueryManager.vue';
import QueryResultPanel from '@/components/query/editor/QueryResultPanel.vue';
import QueryHistoryDisplay from '@/components/query/QueryHistoryDisplay.vue';
import SaveQueryModal from '@/components/query/SaveQueryModal.vue';
import QueryParamsDialog from '@/components/query/dialogs/QueryParamsDialog.vue';

// 导入stores
import {useQueryStore} from '@/stores/query';
import {useDataSourceStore} from '@/stores/datasource';

// 导入类型
import type {QueryParameter, QueryType} from '@/types/query';
import type {DataSource} from '@/types/datasource';
import type {LogicalOperator} from '@/types/builder';

// 导入composable API
import {useQueryVersion} from '@/composables/useQueryVersion';
import {useQueryState} from './composables/useQueryState';
import {useQuerySave} from './composables/useQuerySave';
import {useQueryExecution} from './composables/useQueryExecution';
import {useQueryDraft} from './composables/useQueryDraft';
import {useQueryValidation} from './composables/useQueryValidation';
import {useQueryNavigation} from './composables/useQueryNavigation';

// 导入queryService
import {queryService} from '@/services/query';
import instance from '@/utils/axios';

// 添加到文件顶部的全局变量声明
declare global {
  interface Window {
    __SQL_CONTENT__?: string;
    __REAL_SQL_CONTENT__?: string;
    updateSqlEditor?: (value: string) => boolean;
    fixSqlEditor?: () => boolean;
  }
}

// 定义props
const props = defineProps({
  queryId: {
    type: String,
    default: undefined
  },
  initialDataSourceId: {
    type: String,
    default: ''
  },
  initialSql: {
    type: String,
    default: ''
  },
  initialQueryType: {
    type: String,
    default: 'SQL'
  }
});

// 初始化路由和store
const route = useRoute();
const router = useRouter();
const queryStore = useQueryStore();
const dataSourceStore = useDataSourceStore();

// 组件状态标志
const isComponentMounted = ref(true);

// 查询状态
const queryError = ref<string | null>(null);
const statusMessage = ref<string | null>(null);
const errorMessage = ref<string | null>(null);
const lastDraftSaveAt = ref<string | null>(null);

// 使用composable函数
const {
  versionStatus,
  isActiveVersion,
  availableVersions,
  selectedVersion,
  queryVersion,
  publishedAt,
  lastEditedAt,
  deprecatedAt,
  createNewVersion,
  publishVersion,
  loadVersions
} = useQueryVersion();

// 查询状态管理
const queryStateResult = useQueryState();
const activeTab = queryStateResult.activeTab;

// 查询保存管理
const querySaveResult = useQuerySave();
const saveQuery = () => {
  // 在打开保存对话框前，确保获取当前界面上显示的数据源ID
  const dataSourcePanelVisibleId = document.querySelector('[data-current-datasource-id]')?.getAttribute('data-current-datasource-id');
  if (dataSourcePanelVisibleId && dataSourcePanelVisibleId !== selectedDataSourceId.value) {
    console.log('检测到界面上显示的数据源ID与当前状态不一致，进行同步');
    console.log('- 界面数据源ID:', dataSourcePanelVisibleId);
    console.log('- 当前状态数据源ID:', selectedDataSourceId.value);

    // 更新为界面上显示的ID
    selectedDataSourceId.value = dataSourcePanelVisibleId;
    console.log('已同步数据源ID为界面上的值:', selectedDataSourceId.value);
  }

  console.log('保存查询前的selectedDataSourceId:', selectedDataSourceId.value);
  console.log('当前数据源列表:', JSON.stringify(dataSourceStore.dataSources.map((ds: any) => ({ id: ds.id, name: ds.name }))));
  console.log('当前选中的数据源对象:', selectedDataSource.value);

  // 在打开保存对话框之前，确保当前选择的数据源ID与界面上展示的一致
  const currentDs = dataSourceStore.dataSources.find((ds: any) => ds.id === selectedDataSourceId.value);
  if (!currentDs) {
    console.warn('当前选择的数据源ID不在可用数据源列表中，尝试修正...');
    // 不自动选择数据源，让用户自己选择
    // if (dataSourceStore.dataSources.length > 0) {
    //   selectedDataSourceId.value = dataSourceStore.dataSources[0].id;
    //   console.log('已修正数据源ID为:', selectedDataSourceId.value);
    // }
  }

  // 如果有已存在的查询ID，确保更新其数据源ID为当前选择的数据源ID
  if (currentQueryId.value) {
    console.log('强制更新查询的数据源ID为当前选择的ID:', selectedDataSourceId.value);
    // 更新本地存储和服务端存储，将在下次加载时正确显示
    queryStore.updateQueryDataSource(currentQueryId.value, selectedDataSourceId.value);
  }

  // 打印最终传入保存对话框的数据
  console.log('打开保存对话框前最终验证的数据源ID:', selectedDataSourceId.value);
  querySaveResult.openSaveModal();
};
const handleSaveQuery = (saveData: any) => {
  console.log('处理保存查询，当前数据源ID:', selectedDataSourceId.value);
  console.log('从对话框接收到的saveData:', saveData);
  console.log('当前数据源对象:', selectedDataSource.value);
  console.log('当前选择的Schema:', selectedSchema.value);

  // 准备传递给保存函数的数据
  const queryData = {
    queryType: activeTab.value === 'editor' ? 'sql' : 'natural-language',
    sql: sqlQuery.value,
    naturalLanguageQuery: naturalLanguageQuery.value,
    name: queryName.value,
    dataSourceId: selectedDataSourceId.value,
    schemaName: selectedSchema.value,
    isPublic: isPublic.value
  };

  console.log('传递给保存函数的数据:', queryData);

  // 始终使用当前已选择的数据源ID，这是正确的数据源ID
  saveData.dataSourceId = selectedDataSourceId.value;
  console.log('最终确保使用的数据源ID:', selectedDataSourceId.value);

  querySaveResult.handleSaveQuery(saveData, queryData);
};
const isSaveModalVisible = querySaveResult.isSaveModalVisible;

// 使用查询执行 composable
const queryExecutionResult = useQueryExecution();
const isExecuting = queryExecutionResult.isExecuting;
const cancelQuery = queryExecutionResult.cancelQuery;

// 参数分析和输入相关状态
const showParamsDialog = ref(false);
const queryParameters = ref<QueryParameter[]>([]);
const pendingQueryParams = ref<{
  dataSourceId: string;
  queryText: string;
  queryType: QueryType;
}>({
  dataSourceId: '',
  queryText: '',
  queryType: 'SQL'
});

// 查询编辑器的状态
const selectedDataSourceId = ref<string>('');
const selectedSchema = ref<string>('');
const sqlQuery = ref<string>('');
const naturalLanguageQuery = ref('');
const builderQuery = ref('');
const currentQueryId = ref<string | null>(null);
const currentExecutedSql = ref<string>(''); // 当前执行的SQL语句
const currentExecutedSchemaId = ref<string>(''); // 当前执行查询时使用的schemaId
const queryName = ref('');
const isFavorite = ref(false);
const isPublic = ref(true);
const isDataSourcePanelCollapsed = ref(false); // 数据源面板收起状态
const baseInfoLoaded = ref(false);
const builderState = ref<{
  selectedDataSourceId: string;
  tables: any[];
  selectedTables: string[];
  joins: any[];
  fieldSelections: any[];
  whereConditions: {
    id: string;
    conditions: any[];
    groups: any[];
    logicalOperator: LogicalOperator;
  };
  groupByFields: any[];
  sortDefinitions: any[];
}>({
  selectedDataSourceId: '',
  tables: [],
  selectedTables: [],
  joins: [],
  fieldSelections: [],
  whereConditions: {
    id: '',
    conditions: [],
    groups: [],
    logicalOperator: 'AND'
  },
  groupByFields: [],
  sortDefinitions: []
});
const queryBuilderRef = ref(null);
const queryManagerRef = ref(null);

// 此处添加防止重复加载的标志
const isInitialDataSourceLoaded = ref(false);

// 计算属性：是否有未保存更改
const hasChanges = computed(() => {
  // 如果没有查询ID，则处于新建状态，任何输入都是未保存的更改
  if (!currentQueryId.value) {
    return sqlQuery.value.trim().length > 0 || naturalLanguageQuery.value.trim().length > 0;
  }

  // 有查询ID且加载完成，根据最后保存时间判断
  return lastDraftSaveAt.value !== null;
});

// 计算属性：当前选中的数据源
const selectedDataSource = computed<DataSource | null>(() => {
  if (!selectedDataSourceId.value) return null;
  return dataSourceStore.dataSources.find((ds: any) => ds.id === selectedDataSourceId.value) || null;
});


// 检查查询是否在收藏中
const isQueryFavorite = (queryId: string) => {
  return queryStore.favorites.some((fav: any) => fav.queryId === queryId);
};

// 从查询ID加载查询内容
const loadQueryById = async (queryId: string) => {
  try {
    console.log('开始加载查询，ID:', queryId);

    // 从API获取查询信息
    const query = await queryStore.getQuery(queryId);

    if (!query) {
      message.error('加载查询失败：未找到该查询');
      return;
    }

    console.log('查询数据加载成功:', JSON.stringify({
      id: query.id,
      name: query.name,
      dataSourceId: query.dataSourceId,
      queryType: query.queryType,
      queryTextLength: query.queryText?.length || 0
    }));

    // 打印完整的查询文本内容，用于调试
    if (query.queryText) {
      console.log('原始查询文本内容:', JSON.stringify(query.queryText));
    }

    // 始终优先设置状态值，不使用异步等待
    currentQueryId.value = query.id;

    // 强制清空编辑器内容，确保不会有旧内容
    sqlQuery.value = '';
    naturalLanguageQuery.value = '';

    // 设置数据源ID
    if (query.dataSourceId) {
      console.log('设置数据源ID:', query.dataSourceId);
      
      // 从currentVersion中获取schemaId
      const schemaId = query.currentVersion?.schemaId || query.schemaId;
      
      selectedDataSourceId.value = query.dataSourceId;
      
      // 加载数据源后处理schema选择
      try {
        await dataSourceStore.getDataSourceById(query.dataSourceId);
        
        // 确保数据源的schemas已经加载
        await dataSourceStore.getSchemas(query.dataSourceId);
        
        // 优先使用查询中保存的schemaId
        if (schemaId) {
          selectedSchema.value = schemaId;
        } else {
          // 如果没有schemaId，尝试使用数据源的默认schema
          const dataSource = dataSourceStore.dataSources.find(ds => ds.id === query.dataSourceId);
          if (dataSource && dataSource.schema) {
            console.log('使用数据源默认schema:', dataSource.schema);
            selectedSchema.value = dataSource.schema;
          }
        }
      } catch (e) {
        console.error('加载数据源详情失败:', e);
      }
    }

    // 设置查询名称
    queryName.value = query.name || '未命名查询';
    isPublic.value = query.isPublic ?? true;

    // 检查并设置查询内容
    if (query.queryText) {
      console.log('查询文本类型:', query.queryType);
      console.log('查询文本内容长度:', query.queryText.length);

      // 直接尝试获取Network面板中显示的SQL内容
      const rawQueryContent = query.queryText;

      // 检查API返回的内容是否为JSON字符串，如果是则解析
      let actualQueryContent = rawQueryContent;
      try {
        if (typeof rawQueryContent === 'string' && (
            rawQueryContent.startsWith('{') ||
            rawQueryContent.startsWith('[') ||
          rawQueryContent.includes('\\"')
        )) {
          // 尝试处理可能的JSON转义
          const parsedContent = JSON.parse(rawQueryContent);
          if (parsedContent && typeof parsedContent === 'object' && parsedContent.hasOwnProperty('sql')) {
            actualQueryContent = parsedContent.sql;
            console.log('从JSON对象中提取SQL内容:', actualQueryContent);
          } else if (typeof parsedContent === 'string') {
            actualQueryContent = parsedContent;
            console.log('从JSON字符串中提取内容:', actualQueryContent);
          }
        }
      } catch (e) {
        console.log('内容不是JSON格式，使用原始内容');
      }

      console.log('最终要设置的查询内容:', actualQueryContent);

      // 直接设置内容不等待
      if (query.queryType === 'SQL' || query.queryType === 'sql') {
        // 设置全局变量
        window.__SQL_CONTENT__ = actualQueryContent;
        console.log('设置全局SQL内容，长度:', actualQueryContent.length);

        // 同步设置内容
        sqlQuery.value = actualQueryContent;
        activeTab.value = 'editor';

        // 发送自定义事件强制更新编辑器内容
        const customEvent = new CustomEvent('sqlEditorFix', {
          detail: { content: actualQueryContent }
        });
        document.dispatchEvent(customEvent);
        console.log('已发送sqlEditorFix事件，内容长度:', actualQueryContent.length);

        // 额外注入一段脚本到页面来强制处理
        const injectScript = document.createElement('script');
        injectScript.textContent = `
          // 强制设置SQL内容的辅助函数
          (function() {
            window.__REAL_SQL_CONTENT__ = ${JSON.stringify(actualQueryContent)};
            console.log('内联脚本: 设置真实SQL内容, 长度=', window.__REAL_SQL_CONTENT__.length);

            // 1秒后尝试更新
            setTimeout(function() {
              if (window.updateSqlEditor) {
                console.log('内联脚本: 尝试更新编辑器内容');
                window.updateSqlEditor(window.__REAL_SQL_CONTENT__);
              } else {
                console.log('内联脚本: updateSqlEditor函数不可用');
              }

              // 再次发送自定义事件
              const customEvent = new CustomEvent('sqlEditorFix', {
                detail: { content: window.__REAL_SQL_CONTENT__ }
              });
              document.dispatchEvent(customEvent);
              console.log('内联脚本: 再次发送sqlEditorFix事件');
            }, 1000);
          })();
        `;
        document.head.appendChild(injectScript);

        // 强制DOM更新后设置编辑器内容
        setTimeout(() => {
          console.log('延时100ms再次设置SQL内容');
          sqlQuery.value = actualQueryContent;

          // 如果编辑器已经初始化，直接设置内容
          if (sqlEditorRef.value) {
            try {
              console.log('通过ref设置编辑器内容');
              const editor = sqlEditorRef.value as any;
              editor.setValue && editor.setValue(actualQueryContent);
            } catch (err) {
              console.error('设置编辑器内容失败:', err);
            }
          }

          // 300ms后再次尝试
          setTimeout(() => {
            console.log('延时300ms第三次设置SQL内容');
            // 直接设置DOM元素的值
            const editorElement = document.querySelector('.monaco-editor textarea');
            if (editorElement) {
              console.log('找到编辑器DOM元素，直接设置');
              try {
                (editorElement as any).value = actualQueryContent;
                // 触发输入事件
                const event = new Event('input', { bubbles: true });
                editorElement.dispatchEvent(event);
              } catch (err) {
                console.error('设置DOM元素内容失败:', err);
              }
            }

            // 再次尝试通过ref设置
            if (sqlEditorRef.value) {
              try {
                console.log('再次通过ref设置编辑器内容');
                const editor = sqlEditorRef.value as any;
                editor.setValue && editor.setValue(actualQueryContent);
              } catch (err) {
                console.error('再次设置编辑器内容失败:', err);
              }
            }

            // 最后尝试通过全局函数强制更新
            if (window.fixSqlEditor) {
              console.log('通过全局修复函数尝试更新');
              window.fixSqlEditor();
            }
          }, 300);
        }, 100);
      } else if (query.queryType === 'natural-language') {
        console.log('设置自然语言查询内容');
        activeTab.value = 'nlq';
        naturalLanguageQuery.value = actualQueryContent;
      }
    } else {
      console.warn('查询没有查询文本内容');
    }

    // 加载版本信息 - 这部分保留但放在最后
    loadVersions(query.id).catch(err => {
      console.error('加载版本信息失败:', err);
    });

    message.success('查询加载成功');

    baseInfoLoaded.value = true;
  } catch (error) {
    console.error('加载查询失败:', error);
    message.error('加载查询失败: ' + (error instanceof Error ? error.message : String(error)));
  }
};

// 根据查询类型获取查询文本
const getQueryTextByType = (queryType: QueryType): string => {
  switch (queryType) {
    case 'SQL':
      return sqlQuery.value;
            case 'natural-language':
      return naturalLanguageQuery.value;
    default:
      return builderQuery.value;
  }
};

// 执行构建器查询
const executeBuilderQuery = () => {
  executeQuery();
};

// 处理表选择
const handleTableSelect = (table: any) => {
  console.log('选中表格:', table.name);
};

// 处理列选择
const handleColumnSelect = (column: any, table: any) => {
  console.log('选中列:', column.name, '表格:', table.name);
};

// 向SQL编辑器插入表名
const insertTableName = (tableName: string) => {
  if (activeTab.value !== 'editor') return;
  sqlQuery.value += ` ${tableName}`;
};

// 向SQL编辑器插入列名
const insertColumnName = (columnName: string) => {
  if (activeTab.value !== 'editor') return;
  sqlQuery.value += ` ${columnName}`;
};

// 发布查询
const publishQuery = async () => {
  try {
    // 验证必填项
    if (!selectedDataSourceId.value) {
      setErrorMessage('请选择数据源后再发布查询');
      return;
    }

    // 验证查询内容
    const queryText = activeTab.value === 'editor' ? sqlQuery.value : naturalLanguageQuery.value;
    if (!queryText || queryText.trim() === '') {
      setErrorMessage('请输入查询内容后再发布');
      return;
    }

    // 优先从 URL 参数获取查询ID，如果没有再用当前内存中的ID
    const queryIdFromUrl = route.query.id as string;
    const effectiveQueryId = queryIdFromUrl || currentQueryId.value;

    if (!effectiveQueryId) {
      message.error('请先保存查询再发布');
      return;
    }

    // 确保 currentQueryId 与 URL 同步
    if (queryIdFromUrl && !currentQueryId.value) {
      currentQueryId.value = queryIdFromUrl;
      console.log('从 URL 参数同步查询 ID:', queryIdFromUrl);
    }

    statusMessage.value = '正在发布查询...';

    // 使用版本composable处理发布逻辑
    const result = await publishVersion({
      queryId: effectiveQueryId, // 使用有效的查询ID
      queryText,
      queryType: activeTab.value === 'editor' ? 'SQL' : (activeTab.value === 'nlq' ? 'natural-language' : 'SQL'),
      queryName: queryName.value,
      dataSourceId: selectedDataSourceId.value
    });

    // 检查发布结果
    if (result && result.success) {
      message.success('查询发布成功');
      statusMessage.value = '查询发布成功，版本已激活';

      // 添加成功后跳转到查询列表页面
      setTimeout(() => {
        statusMessage.value = null;
        router.push('/query/list');
      }, 1500);
    } else if (result && !result.success && result.message === '用户取消发布') {
      // 用户取消发布
      message.success('取消成功');
      statusMessage.value = '已取消发布操作';
      setTimeout(() => {
        statusMessage.value = null;
      }, 2000);
    } else {
      // 发布失败
      message.error('发布查询失败');
      statusMessage.value = '发布查询失败';
      setTimeout(() => {
        statusMessage.value = null;
      }, 5000);
    }
  } catch (error) {
    console.error('发布查询失败:', error);
    
    // 处理其他异常情况
    message.error(error instanceof Error ? error.message : '发布查询失败');
    statusMessage.value = error instanceof Error ? error.message : '发布查询失败';
    setTimeout(() => {
      statusMessage.value = null;
    }, 5000);
  }
};

// 处理查询构建器加载
const handleLoadQuery = (query: { sql: string, state?: any }) => {
  builderQuery.value = query.sql;
  if (query.state && queryBuilderRef.value && 'loadState' in queryBuilderRef.value) {
    // 调用QueryBuilder组件的loadState方法 (如果存在)
    (queryBuilderRef.value as any).loadState(query.state);
  }
};

// 更新查询构建器状态
const updateBuilderState = (state: any) => {
  builderState.value = state;
};

// 处理数据源变更
const handleDataSourceChange = async (dataSourceId: string, dataSource: any) => {
  console.log('数据源变更:', dataSourceId, dataSource);
  console.log('当前选中的数据源ID:', selectedDataSourceId.value);

  // 如果是相同的数据源，不重复加载
  if (selectedDataSourceId.value === dataSourceId) {
    console.log('相同的数据源ID，跳过重复加载');
    return;
  }

  console.log('更新数据源ID: 从', selectedDataSourceId.value, '变为', dataSourceId);

  // 重要：更新数据源ID - 这是界面上当前显示的数据源
  selectedDataSourceId.value = dataSourceId;

  // 如果有当前编辑的查询，立即更新其数据源ID，确保保存时使用正确的数据源
  if (currentQueryId.value) {
    console.log('立即更新当前编辑查询的数据源ID:', dataSourceId);
    queryStore.updateQueryDataSource(currentQueryId.value, dataSourceId);
  }

  // 如果初始加载标志为false，设置为true以防止后续重复加载
  if (!isInitialDataSourceLoaded.value) {
    isInitialDataSourceLoaded.value = true;
  }

  // 其他处理逻辑不变
  // 不清空selectedSchema，让用户重新选择或使用默认值
  // selectedSchema.value = '';

  // 尝试设置默认schema
  try {
    const dataSource = dataSourceStore.dataSources.find(ds => ds.id === dataSourceId);
    if (dataSource) {
      if (dataSource.schema) {
        console.log('数据源变更后设置默认schema:', dataSource.schema);
        selectedSchema.value = dataSource.schema;
      } else {
        console.log('数据源没有默认schema，设置为空字符串');
        selectedSchema.value = '';
      }
    } else {
      console.log('未找到数据源，设置schema为空字符串');
      selectedSchema.value = '';
    }
  } catch (e) {
    console.error('设置默认schema失败:', e);
    selectedSchema.value = '';
  }

  // 重置执行状态
  isExecuting.value = false;
  queryError.value = null;
};

// 处理Schema选择
const handleSchemaSelection = async (schema: string) => {
  console.log('选择Schema:', schema);
  console.log('Schema选择详情:', {
    schema,
    schemaType: typeof schema,
    schemaEmpty: !schema || schema === '',
    previousSchema: selectedSchema.value
  });
  selectedSchema.value = schema;

  // 当schema为空字符串时，表示数据源没有Schema配置
  if (schema === '') {
    console.log('数据源无Schema配置，直接加载表和字段');
    // 可以在此处添加额外处理逻辑
  } else {
    // 当schema不为空时，加载对应的表信息
    await handleSchemaSelectionWithTables(schema);
  }
};

// 处理数据源面板收起/展开切换
const handleToggleDataSourcePanel = () => {
  isDataSourcePanelCollapsed.value = !isDataSourcePanelCollapsed.value;
  // 保存到localStorage
  localStorage.setItem('dataSourcePanelCollapsed', isDataSourcePanelCollapsed.value.toString());
};

// 获取Schema的显示名称
const getSchemaDisplayName = (schemaId: string) => {
  if (!schemaId) return '';
  
  // 从数据源store中获取schema信息
  const schemas = dataSourceStore.metadataState?.schemas?.get(selectedDataSourceId.value) || [];
  const schema = schemas.find((s: any) => s.id === schemaId || s.value === schemaId);
  
  if (schema) {
    return schema.name || schema.value || schemaId;
  }
  
  // 如果没找到，直接返回schemaId
  return schemaId;
};

// 处理Schema选择并加载表信息
const handleSchemaSelectionWithTables = async (schemaId: string) => {
  console.log('设置Schema:', schemaId, '，让组件自动加载表信息');
  
  // 直接设置selectedSchema，让组件的响应式系统处理后续的表信息加载
  selectedSchema.value = schemaId;
  
  // 等待一下让组件有时间处理schema变化
  await nextTick();
  
  console.log('Schema已设置为:', selectedSchema.value);
};

// 定义编辑器引用
const sqlEditorRef = ref<any>(null);

// 执行查询
const executeQuery = async (errorMessage?: string, selectedSql?: string) => {
  // 如果有错误消息，直接显示错误
  if (errorMessage) {
    message.error(errorMessage);
    return;
  }
  
  statusMessage.value = '正在执行查询...';
  isExecuting.value = true;
  queryError.value = null;

  try {
    // 获取界面上显示的数据源ID，确保使用最新选择的数据源
    const dataSourcePanelVisibleId = document.querySelector('[data-current-datasource-id]')?.getAttribute('data-current-datasource-id');
    if (dataSourcePanelVisibleId && dataSourcePanelVisibleId !== selectedDataSourceId.value) {
      console.log('检测到界面上显示的数据源ID与当前状态不一致，进行同步');
      console.log('- 界面数据源ID:', dataSourcePanelVisibleId);
      console.log('- 当前状态数据源ID:', selectedDataSourceId.value);

      // 更新为界面上显示的ID
      selectedDataSourceId.value = dataSourcePanelVisibleId;
      console.log('已同步数据源ID为界面上的值:', selectedDataSourceId.value);
    }

    let queryText = '';
    let queryType: QueryType = 'SQL';

    // 根据activeTab确定查询文本和类型
    if (activeTab.value === 'editor') {
      // 优先使用选中的SQL内容，如果没有选中则使用全部内容
      queryText = selectedSql || sqlQuery.value || '';
      queryType = 'SQL';
      console.log('SQL编辑器模式，使用查询文本:', {
        选中SQL: selectedSql ? '是' : '否',
        选中SQL长度: selectedSql?.length || 0,
        全部内容长度: sqlQuery.value?.length || 0,
        最终使用长度: queryText.length
      });
    } else if (activeTab.value === 'nlq') {
      queryText = naturalLanguageQuery.value || '';
              queryType = 'natural-language';
    } else if (activeTab.value === 'builder') {
      queryText = builderQuery.value || '';
      queryType = 'SQL'; // 构建器生成的也是SQL
    }

    console.log(`[DEBUG] 确定查询内容: 类型=${queryType}, 长度=${queryText.length}, 预览="${queryText.substring(0, 50)}${queryText.length > 50 ? '...' : ''}"`);

    // 保存当前执行的SQL和schemaId，用于执行计划
    currentExecutedSql.value = queryText;
    currentExecutedSchemaId.value = selectedSchema.value;
    
    console.log('执行查询时保存的参数:', {
      sql: queryText.substring(0, 100) + (queryText.length > 100 ? '...' : ''),
      selectedSchema: selectedSchema.value,
      currentExecutedSchemaId: currentExecutedSchemaId.value,
      schemaIdEmpty: !selectedSchema.value || selectedSchema.value === ''
    });

    // 检查必要参数
    if (!selectedDataSourceId.value) {
      throw new Error('请选择数据源');
    }

    if (!queryText || !queryText.trim()) {
      throw new Error('查询内容不能为空');
    }

    // 原始SQL保存，用于参数解析
    const originalSql = queryText;

    // 处理SQL参数格式，将#{param}转换为${param}
    if (queryType === 'SQL') {
      // 检查是否有非法前缀，如果有则移除
      if (queryText.startsWith('123')) {
        console.log('检测到SQL前缀"123"，移除此前缀');
        queryText = queryText.substring(3);
      }
    }

    const params = {
      dataSourceId: selectedDataSourceId.value,
      queryText: queryText,
      sql: queryText,
      queryType: queryType,
      isNewQuery: true, // 标记为新增查询，避免使用查询ID的API
      page: 1, // 默认第一页
      size: 20, // 默认每页20条
      schemaId: selectedSchema.value // 新增：传递选择的schema ID
    };

    console.log('执行查询参数（包含分页）:', JSON.stringify(params));

    // 只有SQL查询才需要分析参数
    if (params.queryType === 'SQL') {
      try {
        console.log('开始分析SQL中的参数');

        // 使用参数分析服务分析SQL中的参数
        const parameters = await queryService.analyzeQueryParameters({
          dataSourceId: params.dataSourceId,
          sql: params.sql  // 确保使用正确的参数名
        });

        console.log('分析出的参数:', parameters);

        // 如果有参数，显示参数输入对话框
        if (parameters && parameters.length > 0) {
          console.log(`检测到${parameters.length}个参数，显示参数输入对话框`);
          queryParameters.value = parameters;
          pendingQueryParams.value = { ...params };

          // 确保UI更新后再显示对话框
          await nextTick(() => {
            showParamsDialog.value = true;
            console.log('参数对话框已显示:', showParamsDialog.value);
          });

          // 中断执行流程，等待用户在对话框中输入参数后继续
          isExecuting.value = false;
          return;
        }

        console.log('未检测到参数，直接执行查询');
      } catch (error) {
        console.error('分析SQL参数失败:', error);
        // 参数分析失败时显示警告但继续执行
        message.warning('参数分析失败，将直接执行查询');
      }
    }

    // 没有参数或不是SQL查询，直接执行
    console.log('调用executeQuery开始执行查询', params);

    // 保存查询参数，用于分页时重用
    currentQueryParams.value = {
      dataSourceId: params.dataSourceId,
      sql: params.sql,
      queryType: params.queryType,
      parameters: params.parameters || {},
      isNewQuery: params.isNewQuery || true,
      page: params.page || 1,
      size: params.size || 20, // 保存页面大小
      schemaId: params.schemaId // 保存schema ID
    };

    const result = await queryExecutionResult.executeQuery(params);
    console.log('查询执行完成，结果:', result);

    // 处理查询结果
    if (result) {
      handleQueryResult(result);
    }

    return result;
  } catch (error) {
    console.error('执行查询失败:', error);
    message.error(error instanceof Error ? error.message : '执行查询失败');
    queryError.value = error instanceof Error ? error.message : '执行查询失败';
  } finally {
    isExecuting.value = false;
  }
};

/**
 * 处理参数确认并执行查询
 * @param paramValues 用户输入的参数值
 */
const handleParamsConfirm = async (paramValues: Record<string, any>) => {
  console.log('确认的参数值:', paramValues);

  // 获取界面上显示的数据源ID
  const dataSourcePanelVisibleId = document.querySelector('[data-current-datasource-id]')?.getAttribute('data-current-datasource-id');
  if (dataSourcePanelVisibleId && dataSourcePanelVisibleId !== selectedDataSourceId.value) {
    console.log('参数确认时，检测到界面数据源ID与当前状态不一致，进行同步');
    console.log('- 界面数据源ID:', dataSourcePanelVisibleId);
    console.log('- 当前状态数据源ID:', selectedDataSourceId.value);

    // 更新为界面上显示的ID
    selectedDataSourceId.value = dataSourcePanelVisibleId;
    console.log('已同步数据源ID为界面上的值:', selectedDataSourceId.value);
  }

  // 获取当前SQL文本
  const sqlText = sqlQuery.value;

  // 检查SQL是否为空
  if (!sqlText || sqlText.trim() === '') {
    message.error('SQL查询不能为空，请在编辑器中输入查询语句');
    return;
  }

  // 检查是否有非法前缀，如果有则移除
  let sqlToExecute = sqlText;
  if (sqlToExecute.startsWith('123')) {
    console.log('检测到SQL前缀"123"，移除此前缀');
    sqlToExecute = sqlToExecute.substring(3);
  }

  // 为LIKE条件特殊处理参数值 - 添加通配符到参数值中
  const processedParams: Record<string, any> = { ...paramValues };

  try {
    // 检查是否有选择数据源
    if (!selectedDataSourceId.value) {
      throw new Error('缺少必要参数: dataSourceId');
    }

    console.log('执行查询，参数:', {
      dataSourceId: selectedDataSourceId.value,
      sqlLength: sqlToExecute.length,
      parametersCount: Object.keys(processedParams).length,
      parameters: processedParams
    });

    // 保存查询参数，用于分页时重用
    currentQueryParams.value = {
      dataSourceId: selectedDataSourceId.value,
      sql: sqlToExecute,
      queryType: 'SQL',
      parameters: processedParams,
      isNewQuery: true,
      page: 1,
      size: 20, // 默认每页20条
      schemaId: selectedSchema.value // 直接使用当前选择的schema ID
    };

    // 执行查询 - 使用对象形式的参数
    const result = await queryService.executeQuery({
      dataSourceId: selectedDataSourceId.value,
      sql: sqlToExecute,
      parameters: processedParams, // 使用处理后的参数对象
      isNewQuery: true, // 标记为新增查询，避免使用查询ID的API
      queryType: 'SQL',
      page: 1, // 默认第一页
      size: 20, // 默认每页20条
      schemaId: selectedSchema.value // 直接使用当前选择的schema ID
    });

    console.log('查询执行成功，获得结果:', result);

    // 处理查询结果
    handleQueryResult(result);

    // 关闭参数对话框
    showParamsDialog.value = false;
  } catch (error: any) {
    console.error('执行查询失败:', error);
    queryError.value = error.message || '执行查询失败';
    message.error(error.message || '执行查询失败');
  } finally {
    isExecuting.value = false;
  }
};

/**
 * 处理查询结果
 * @param result 查询执行结果
 */
const handleQueryResult = (result: any) => {
  // 更新查询结果到store
  if (result) {
    try {
      // 初始化一个本地的结果对象
      const localResult = { ...result };

      // 直接设置结果，不调用init()方法避免加载收藏夹和历史记录
      // 使用store的响应式特性直接设置currentQueryResult
      queryStore.currentQueryResult = localResult;

      // 如果结果包含成功信息，显示成功提示
      if (localResult.success === true) {
        const rowCount = localResult.data?.rowCount || 0;
        statusMessage.value = `查询执行成功，返回 ${rowCount} 条记录`;
      } else if (localResult.success === false) {
        // 处理查询失败情况
        queryError.value = localResult.message || '查询执行失败';
        statusMessage.value = queryError.value;
      }
    } catch (error) {
      console.error('设置查询结果时出错:', error);
      queryError.value = '处理查询结果时发生错误';
      message.error('处理查询结果时发生错误');
    }
  }
};

// 添加处理取消的方法
const handleParamsCancel = () => {
  console.log('用户取消参数输入');
  showParamsDialog.value = false;
  isExecuting.value = false;
};

// 导出结果
const exportResults = async (format: string) => {
  console.log('导出函数被调用，格式:', format);
  
  if (format !== 'excel') {
    console.error('不支持的导出格式:', format);
    return;
  }

  try {
    // 检查是否有查询结果
    if (!queryStore.currentQueryResult) {
      console.log('没有查询结果');
      message.error('没有可导出的查询结果，请先执行查询');
      return;
    }

    // 构建导出参数 - 优先使用保存的参数，否则从当前状态构建
    let exportParams = currentQueryParams.value;
    
    if (!exportParams) {
      console.log('没有保存的查询参数，从当前状态构建参数');
      
      // 检查必要的数据源ID
      if (!selectedDataSourceId.value) {
        message.error('缺少数据源信息，无法导出');
        return;
      }
      
      // 从当前状态构建查询参数
      // 尝试从查询结果中获取参数信息
      let parameters = {};
      if (queryStore.currentQueryResult?.parameters) {
        parameters = queryStore.currentQueryResult.parameters;
      }
      
      exportParams = {
        dataSourceId: selectedDataSourceId.value,
        sql: sqlQuery.value,
        queryType: 'SQL',
        parameters: parameters,
        isNewQuery: true, // 添加这个参数，与执行查询保持一致
        page: 1, // 添加分页参数
        size: 10,
        schemaId: selectedSchema.value
      };
      
      console.log('从当前状态构建的导出参数:', exportParams);
    } else {
      console.log('使用保存的查询参数:', exportParams);
    }

    console.log('开始导出Excel，使用参数:', exportParams);
    console.log('参数详情:');
    console.log('- dataSourceId:', exportParams.dataSourceId);
    console.log('- sql:', exportParams.sql);
    console.log('- queryType:', exportParams.queryType);
    console.log('- parameters:', exportParams.parameters);
    console.log('- size:', exportParams.size);
    console.log('- schemaId:', exportParams.schemaId);

    // 显示加载提示
    const loadingMessage = message.loading('正在导出Excel文件...', 0);

    // 调用Excel导出接口 - 使用项目统一的axios实例
    const response = await instance.post('/api/excel/export-sql-result', exportParams, {
      responseType: 'blob'
    });

    // 关闭加载提示
    loadingMessage();

    console.log('响应对象:', response);
    console.log('响应数据类型:', typeof response.data);
    console.log('响应数据是否为Blob:', response.data instanceof Blob);

    // 获取文件名
    let filename = 'query_result.xlsx';
    
    try {
      // 尝试从响应头获取文件名
      const contentDisposition = response.headers?.['content-disposition'] || response.headers?.['Content-Disposition'];
      
      console.log('响应头信息:', response.headers);
      console.log('Content-Disposition:', contentDisposition);
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '');
          // URL解码文件名
          try {
            filename = decodeURIComponent(filename);
          } catch (e) {
            console.warn('文件名解码失败，使用原始文件名:', filename);
          }
        }
      } else {
        // 如果没有Content-Disposition头，生成默认文件名
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        filename = `SQL查询结果_${timestamp}.xlsx`;
        console.log('未找到Content-Disposition头，使用默认文件名:', filename);
      }
    } catch (e) {
      console.warn('获取文件名失败，使用默认文件名:', e);
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
      filename = `SQL查询结果_${timestamp}.xlsx`;
    }

    // 下载文件
    let blob;
    if (response.data instanceof Blob) {
      blob = response.data;
    } else {
      blob = new Blob([response.data]);
    }
    
    console.log('创建的Blob:', blob);
    console.log('Blob大小:', blob.size);
    console.log('Blob类型:', blob.type);
    
    const downloadUrl = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = downloadUrl;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(downloadUrl);
    document.body.removeChild(a);

    message.success('Excel文件导出成功');
  } catch (error) {
    console.error('导出Excel失败:', error);
    message.error(`导出失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

// 保存当前查询参数，用于分页时重用
const currentQueryParams = ref<{
  dataSourceId: string;
  sql: string;
  queryType: string;
  parameters: Record<string, any>;
  size?: number;
  schemaId?: string; // 新增：schema ID
} | null>(null);

// 处理分页变化
const handlePageChange = async (page: number) => {
  console.log('当前保存的查询参数:', currentQueryParams.value);

  if (!currentQueryParams.value) {
    console.warn('没有保存的查询参数，无法执行分页查询');
    return;
  }

  try {
    isExecuting.value = true;
    queryError.value = null;

    // 构建分页查询参数
    const paginationParams = {
      ...currentQueryParams.value,
      page: page,
      size: currentQueryParams.value.size || 20 // 使用当前页面大小或默认20
    };

    console.log('发送分页查询请求，参数:', paginationParams);

    // 使用保存的查询参数，添加分页参数
    const result = await queryService.executeQuery(paginationParams);

    console.log('分页查询执行成功，获得结果:', result);
    
    // 检查结果是否为空 - 更全面的检查
    let isEmpty = false;
    
    if (result && result.data) {
      const rowCount = result.data.rowCount || 0;
      const rows = result.data.rows || [];
      const total = result.data.total || 0;
      
      // 检查多种空结果情况
      if (rowCount === 0 || rows.length === 0 || total === 0) {
        isEmpty = true;
      }
    } else if (result && result.rows) {
      // 检查直接返回rows的情况
      if (!result.rows || result.rows.length === 0) {
        isEmpty = true;
      }
    } else if (!result || !result.data) {
      // 检查完全没有结果的情况
      isEmpty = true;
    }
    
    // 如果下一页没有数据，且当前已经有查询结果，则显示提示信息但不清空当前结果
    if (isEmpty && queryStore.currentQueryResult) {
      message.warning('没有更多数据了');
      // 不调用handleQueryResult，保持当前页面的数据
      // 立即重置加载状态，让用户可以继续操作
      isExecuting.value = false;
      // 触发分页加载完成事件，让QueryResults组件重置加载状态
      nextTick(() => {
        // 通过更新一个时间戳来触发QueryResults组件的重置
        queryStore.currentQueryResult = {
          ...queryStore.currentQueryResult,
          _paginationComplete: Date.now()
        };
      });
      return;
    }
    
    // 如果是第一页或者当前没有查询结果，即使是空结果也要正常处理
    if (isEmpty && page === 1) {
      // 第一页没有数据，正常处理空结果
      handleQueryResult(result);
      return;
    }
    
    // 有数据时正常处理结果
    handleQueryResult(result);
  } catch (error: any) {
    console.error('分页查询失败:', error);
    queryError.value = error.message || '分页查询失败';
    message.error(error.message || '分页查询失败');
    // 确保在错误情况下也重置加载状态
    isExecuting.value = false;
  } finally {
    // 确保最终状态被重置
    isExecuting.value = false;
  }
};

// 处理每页条数变化
const handlePageSizeChange = async (size: number) => {
  console.log('当前保存的查询参数:', currentQueryParams.value);

  if (!currentQueryParams.value) {
    console.warn('没有保存的查询参数，无法执行分页查询');
    return;
  }

  try {
    isExecuting.value = true;
    queryError.value = null;

    // 构建页面大小变更查询参数
    const pageSizeParams = {
      ...currentQueryParams.value,
      page: 1,
      size: size
    };

    console.log('发送页面大小变更查询请求，参数:', pageSizeParams);

    // 使用保存的查询参数，添加分页参数，重置为第一页
    const result = await queryService.executeQuery(pageSizeParams);

    // 更新保存的查询参数中的页面大小
    currentQueryParams.value.size = size;

    console.log('每页条数变化查询执行成功，获得结果:', result);
    handleQueryResult(result);
  } catch (error: any) {
    console.error('每页条数变化查询失败:', error);
    queryError.value = error.message || '查询失败';
    message.error(error.message || '查询失败');
  } finally {
    isExecuting.value = false;
  }
};

// 使用查询草稿 composable
const queryDraftResult = useQueryDraft();
const formatLastSavedTime = queryDraftResult.formatLastSavedTime;

// 使用查询验证 composable
const queryValidationResult = useQueryValidation();
const clearErrorMessage = () => {
  errorMessage.value = null;
};
const setErrorMessage = (msg: string) => {
  errorMessage.value = msg;
};

// 使用查询导航 composable
const queryNavigationResult = useQueryNavigation();
const backUrl = computed(() => '/query/list');
const returnToList = () => {
  router.push('/query/list');
};

// 重置执行状态
const resetExecutionState = () => {
  isExecuting.value = false;
  queryError.value = null;
  queryStore.currentQueryResult = null;
};

// 处理来自QueryEditorTabs的执行事件
const handleExecuteFromTabs = () => {
  // 如果是SQL编辑器模式，尝试获取选中的SQL内容
  if (activeTab.value === 'editor' && sqlEditorRef.value) {
    // 通过SqlEditor组件的方法获取选中的文本
    const selectedText = sqlEditorRef.value.getSelectedText();
    if (selectedText && selectedText.trim()) {
      executeQuery(undefined, selectedText);
      return;
    }
  }
  
  // 没有选中文本或不是SQL编辑器模式，执行全部内容
  executeQuery();
};

// 组件挂载
onMounted(async () => {
  isComponentMounted.value = true;


  // 加载数据源
  await dataSourceStore.fetchDataSources();

  // 获取当前URL中的查询ID参数，判断是新增还是编辑
  const queryId = route.params.id as string || route.query.id as string;
  const isNewQuery = !queryId || queryId === 'new';

  // 新增查询时，不设置默认数据源，让用户自己选择
  // if (isNewQuery && dataSourceStore.activeDataSources.length > 0 && !selectedDataSourceId.value) {
  //   selectedDataSourceId.value = dataSourceStore.activeDataSources[0].id;
  // }

  // 处理查询ID - 优先使用props中的queryId，然后是URL参数
  const effectiveQueryId = props.queryId || queryId;
  if (effectiveQueryId && effectiveQueryId !== 'new') {
    // 设置当前查询ID
    currentQueryId.value = effectiveQueryId;
    console.log('从URL或props获取查询ID:', effectiveQueryId);

    // 在控制台输出当前查询ID，用于调试
    console.log('设置当前查询ID变量:', currentQueryId.value);

    // 加载查询详情
    await loadQueryById(effectiveQueryId);

    // 设置一个递增重试计数器
    let retryCount = 0;
    const maxRetries = 5;

    // 创建一个重试函数，确保内容被正确设置
    const ensureSqlContentDisplayed = () => {
      retryCount++;
      console.log(`尝试确保SQL内容显示(第${retryCount}次)`);

      if (sqlEditorRef.value) {
        console.log('找到SQL编辑器ref，尝试读取内容');

        try {
          // 使用as any类型转换
          const editor = sqlEditorRef.value as any;

          if (typeof editor.getValue === 'function') {
            const currentValue = editor.getValue();
            console.log(`编辑器当前内容长度: ${currentValue.length}`);

            // 检查内容是否为空
            if (!currentValue && sqlQuery.value) {
              console.log('编辑器内容为空，但有缓存内容，尝试设置');

              if (typeof editor.setValue === 'function') {
                editor.setValue(sqlQuery.value);
                console.log('已通过setValue设置内容');
              }

              // 使用全局函数再次尝试，确保跨组件设置
              if (window.updateSqlEditor) {
                const result = window.updateSqlEditor(sqlQuery.value);
                console.log('通过全局函数设置内容，结果:', result);
              }

              // 设置全局变量
              window.__SQL_CONTENT__ = sqlQuery.value;
            }
          }
        } catch (err) {
          console.error('访问SQL编辑器出错:', err);
        }
      } else {
        console.warn('SQL编辑器ref不可用');
      }

      // 如果未达到最大重试次数且编辑器内容仍为空，继续重试
      if (retryCount < maxRetries) {
        // 使用递增延迟，避免过度重试
        const delay = 500 + (retryCount * 200);
        setTimeout(ensureSqlContentDisplayed, delay);
      }
    };

    // 开始第一次重试检查
    setTimeout(ensureSqlContentDisplayed, 1000);
  } else {
    // 新增查询场景，默认设置为V1
    console.log('新增查询场景，默认设置版本号为V1');
    baseInfoLoaded.value = true;
    selectedVersion.value = 'V1';
    queryVersion.value = 'V1';
    availableVersions.value = ['V1'];
    versionStatus.value = 'DRAFT';
  }

  // 添加全局键盘事件监听
  document.addEventListener('keydown', handleGlobalKeyDown);

  // 初始化数据源面板收起状态
  const savedCollapsedState = localStorage.getItem('dataSourcePanelCollapsed');
  if (savedCollapsedState !== null) {
    isDataSourcePanelCollapsed.value = savedCollapsedState === 'true';
  }
});

// 全局键盘事件处理
const handleGlobalKeyDown = (event: KeyboardEvent) => {
  // Ctrl+S 保存草稿
  if ((event.ctrlKey || event.metaKey) && event.key === 's') {
    event.preventDefault(); // 阻止浏览器默认的保存页面行为
    saveQuery();
    return;
  }

  // Ctrl+Enter 执行查询
  if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
    event.preventDefault();
    executeQuery();
  }
};

// 组件卸载时
onUnmounted(() => {
  isComponentMounted.value = false;

  // 删除可能的DOM事件监听器
  document.removeEventListener('keydown', handleGlobalKeyDown);
});
</script>

<style scoped>
.loading-spinner {
  border: 3px solid rgba(156, 163, 175, 0.3);
  border-radius: 50%;
  border-top: 3px solid #6366f1;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 消息渐变动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

/* 调整滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* 强制添加边框的重要边框 */
.force-border {
  border: 1px solid #d1d5db !important;
}
</style>
