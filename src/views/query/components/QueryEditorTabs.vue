<template>
  <div class="query-editor-tabs">
    <a-tabs v-model:activeKey="activeTabKey" @change="handleTabChange">
      <a-tab-pane key="sql" tab="SQL">
        <div class="sql-editor-container">
          <a-form-item
            :validate-status="sqlEditorValidateStatus"
            :help="sqlEditorValidateMessage"
          >
            <sql-editor
              v-model:value="sqlContent"
              :disabled="disabled"
              :loading="loading"
              @execute="handleExecute"
              @cancel="handleCancel"
              :auto-focus="true"
              :read-only="readOnly"
            />
          </a-form-item>
        </div>
      </a-tab-pane>
      <a-tab-pane key="natural-language" tab="自然语言" :disabled="true">
        <div class="natural-language-container">
          <a-form-item
            :validate-status="nlQueryValidateStatus"
            :help="nlQueryValidateMessage"
          >
            <natural-language-query
              v-model:value="nlContent"
              :disabled="disabled"
              :loading="loading"
              @execute="handleNlExecute"
              @cancel="handleCancel"
              :read-only="readOnly"
            />
          </a-form-item>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import SqlEditor from '@/components/query/SqlEditor.vue';
import NaturalLanguageQuery from '@/components/query/NaturalLanguageQuery.vue';
import type { QueryEditorTabsProps } from '@/views/query/types/queryEditor';

const props = defineProps<QueryEditorTabsProps>();
const emit = defineEmits(['update:sqlContent', 'update:nlContent', 'execute', 'nlExecute', 'cancel', 'tabChange']);

// 本地状态
const activeTabKey = ref(props.defaultActiveKey || 'sql');

// 监听内容变化，向父组件传递更新
watch(() => props.sqlContent, (val) => {
  // 避免循环更新
  if (val !== sqlContent.value) {
    sqlContent.value = val;
  }
}, { immediate: true });

watch(() => props.nlContent, (val) => {
  // 避免循环更新
  if (val !== nlContent.value) {
    nlContent.value = val;
  }
}, { immediate: true });

// 本地代理状态，用于双向绑定
const sqlContent = ref(props.sqlContent || '');
const nlContent = ref(props.nlContent || '');

// 当本地内容变化时，通知父组件
watch(sqlContent, (val) => {
  emit('update:sqlContent', val);
});

watch(nlContent, (val) => {
  emit('update:nlContent', val);
});

// 验证状态计算属性
const sqlEditorValidateStatus = computed(() => {
  if (props.sqlValidateStatus) return props.sqlValidateStatus;
  return activeTabKey.value === 'sql' && props.submitFailed && !sqlContent.value ? 'error' : '';
});

const sqlEditorValidateMessage = computed(() => {
  if (props.sqlValidateMessage) return props.sqlValidateMessage;
  return activeTabKey.value === 'sql' && props.submitFailed && !sqlContent.value ? 'SQL 查询不能为空' : '';
});

const nlQueryValidateStatus = computed(() => {
  if (props.nlValidateStatus) return props.nlValidateStatus;
  return activeTabKey.value === 'natural-language' && props.submitFailed && !nlContent.value ? 'error' : '';
});

const nlQueryValidateMessage = computed(() => {
  if (props.nlValidateMessage) return props.nlValidateMessage;
  return activeTabKey.value === 'natural-language' && props.submitFailed && !nlContent.value ? '自然语言查询不能为空' : '';
});

// 事件处理函数
const handleTabChange = (key: string) => {
  // 禁用自然语言查询tab
  if (key === 'natural-language') return;
  emit('tabChange', key);
};

const handleExecute = () => {
  emit('execute', sqlContent.value);
};

const handleNlExecute = () => {
  emit('nlExecute', nlContent.value);
};

const handleCancel = () => {
  emit('cancel');
};
</script>

<style scoped>
.query-editor-tabs {
  margin-top: 16px;
}

.sql-editor-container, .natural-language-container {
  margin-top: 8px;
}
</style>