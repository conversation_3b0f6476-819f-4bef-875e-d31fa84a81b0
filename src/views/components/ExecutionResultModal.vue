<template>
  <a-modal
    v-model:visible="modalVisible"
    title="执行结果"
    width="90%"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="execution-result">
      <div v-if="loading" class="loading-container">
        <a-spin size="large" />
        <p class="loading-text">正在加载执行结果...</p>
      </div>

      <div v-else-if="error" class="error-container">
        <a-result
          status="error"
          title="加载失败"
          :sub-title="error"
        >
          <template #extra>
            <a-button type="primary" @click="handleRetry">
              重试
            </a-button>
          </template>
        </a-result>
      </div>

      <div v-else-if="resultData" class="result-content">
        <!-- 结果统计信息 -->
        <div class="result-stats">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic title="总行数" :value="resultData.totalRows || 0" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="总列数" :value="resultData.totalColumns || 0" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="执行时间" :value="resultData.executionTime || 0" suffix="ms" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="数据大小" :value="resultData.dataSize || 0" suffix="KB" />
            </a-col>
          </a-row>
        </div>

        <!-- 结果表格 -->
        <div class="result-table-container">
          <div class="table-header">
            <h3 class="table-title">查询结果</h3>
            <div class="table-actions">
              <a-button @click="handleExport('excel')">
                <template #icon>
                  <FileExcelOutlined />
                </template>
                导出Excel
              </a-button>
            </div>
          </div>

          <a-table
            :columns="tableColumns"
            :data-source="tableData"
            :pagination="paginationConfig"
            :scroll="{ x: 'max-content', y: 400 }"
            size="small"
            bordered
            :loading="tableLoading"
          >
            <!-- 自定义单元格渲染 -->
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ index + 1 }}
              </template>
              <template v-else>
                <div class="cell-content" :title="String(record[column.dataIndex])">
                  {{ formatCellValue(record[column.dataIndex]) }}
                </div>
              </template>
            </template>
          </a-table>
        </div>

        <!-- 操作按钮 -->
        <div class="result-actions">
          <a-space>
            <a-button @click="handleCancel">
              关闭
            </a-button>
          </a-space>
        </div>
      </div>

      <div v-else class="empty-container">
        <a-empty description="暂无执行结果" />
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  DownloadOutlined,
  FileExcelOutlined
} from '@ant-design/icons-vue';
import { executionHistoryService } from '@/services/executionHistoryService';

// 组件属性
const props = defineProps<{
  visible: boolean;
  resultId: string;
}>();

// 组件事件
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
}>();

// 响应式数据
const loading = ref(false);
const tableLoading = ref(false);
const error = ref('');
const resultData = ref<any>(null);
const tableData = ref<any[]>([]);
const tableColumns = ref<any[]>([]);

// 分页配置
const paginationConfig = ref({
  current: 1,
  pageSize: 100,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => 
    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
  pageSizeOptions: ['50', '100', '200', '500']
});

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 监听resultId变化
watch(() => props.resultId, (newResultId) => {
  if (newResultId && props.visible) {
    loadExecutionResult();
  }
});

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (visible && props.resultId) {
    loadExecutionResult();
  }
});

// 加载执行结果
const loadExecutionResult = async () => {
  if (!props.resultId) return;

  loading.value = true;
  error.value = '';
  
  try {
    const result = await executionHistoryService.getExecutionResult(props.resultId);
    
    if (result.success) {
      resultData.value = result.data;
      
      // 处理表格数据
      if (result.data.rows && result.data.columns) {
        tableData.value = result.data.rows;
        tableColumns.value = result.data.columns.map((col: any, index: number) => ({
          title: col.name || `列${index + 1}`,
          dataIndex: col.key || `col_${index}`,
          key: col.key || `col_${index}`,
          width: 150,
          ellipsis: true
        }));
        
        // 添加序号列
        tableColumns.value.unshift({
          title: '序号',
          key: 'index',
          width: 60,
          align: 'center'
        });
        
        paginationConfig.value.total = result.data.totalRows || result.data.rows.length;
      }
    } else {
      error.value = result.message || '获取执行结果失败';
    }
  } catch (err: any) {
    console.error('获取执行结果失败:', err);
    error.value = err.message || '获取执行结果失败';
  } finally {
    loading.value = false;
  }
};

// 处理重试
const handleRetry = () => {
  loadExecutionResult();
};

// 处理取消
const handleCancel = () => {
  modalVisible.value = false;
};

// 处理导出
const handleExport = (format: 'excel') => {
  if (!resultData.value) {
    message.warning('暂无数据可导出');
    return;
  }

  try {
    if (format === 'excel') {
      exportToExcel();
    }
    message.success(`导出${format.toUpperCase()}成功`);
  } catch (error) {
    message.error(`导出${format.toUpperCase()}失败`);
  }
};

// 导出为CSV
const exportToCSV = () => {
  if (!tableData.value.length || !tableColumns.value.length) return;

  const headers = tableColumns.value
    .filter(col => col.key !== 'index')
    .map(col => col.title);
  
  const csvContent = [
    headers.join(','),
    ...tableData.value.map(row => 
      tableColumns.value
        .filter(col => col.key !== 'index')
        .map(col => `"${String(row[col.dataIndex] || '').replace(/"/g, '""')}"`)
        .join(',')
    )
  ].join('\n');

  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = `execution_result_${Date.now()}.csv`;
  link.click();
};

// 导出为Excel
const exportToExcel = () => {
  // 这里可以使用第三方库如 xlsx 来导出Excel
  // 暂时使用CSV格式
  exportToCSV();
};

// 格式化单元格值
const formatCellValue = (value: any) => {
  if (value === null || value === undefined) {
    return '-';
  }
  
  if (typeof value === 'object') {
    return JSON.stringify(value);
  }
  
  return String(value);
};

// 组件挂载时加载数据
onMounted(() => {
  if (props.visible && props.resultId) {
    loadExecutionResult();
  }
});
</script>

<style scoped>
.execution-result {
  max-height: 80vh;
  overflow-y: auto;
}

.loading-container,
.error-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.loading-text {
  margin-top: 16px;
  color: #8c8c8c;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.result-stats {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.result-table-container {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.table-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.cell-content {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.result-actions {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .table-actions {
    justify-content: center;
  }
  
  .result-stats :deep(.ant-col) {
    margin-bottom: 16px;
  }
}
</style>
