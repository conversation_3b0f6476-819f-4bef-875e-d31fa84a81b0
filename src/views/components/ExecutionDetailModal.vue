<template>
  <a-modal
    v-model:visible="modalVisible"
    title="执行详情"
    width="800px"
    :footer="null"
    :z-index="2000"
    :mask-closable="true"
    @cancel="handleCancel"
  >
    <div v-if="executionData" class="execution-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <div class="detail-grid">
          <div class="detail-item">
            <label class="detail-label">执行ID:</label>
            <span class="detail-value">{{ executionData.id }}</span>
          </div>
          <div class="detail-item">
            <label class="detail-label">执行时间:</label>
            <span class="detail-value">{{ formatDateTime(executionData.executedAt) }}</span>
          </div>
          <div class="detail-item">
            <label class="detail-label">执行状态:</label>
            <a-tag :color="getStatusColor(executionData.status)">
              <template #icon>
                <component :is="getStatusIcon(executionData.status)" />
              </template>
              {{ getStatusLabel(executionData.status) }}
            </a-tag>
          </div>
          <div class="detail-item">
            <label class="detail-label">执行耗时:</label>
            <span class="detail-value">{{ executionData.duration }}ms</span>
          </div>
          <div class="detail-item">
            <label class="detail-label">执行人:</label>
            <span class="detail-value">{{ executionData.executedBy || '-' }}</span>
          </div>
          <div class="detail-item">
            <label class="detail-label">数据源:</label>
            <span class="detail-value">{{ executionData.dbSchemaName || '-' }}</span>
          </div>
          <div class="detail-item">
            <label class="detail-label">返回行数:</label>
            <span class="detail-value">{{ executionData.rowCount }}</span>
          </div>
          <div class="detail-item">
            <label class="detail-label">查询ID:</label>
            <span class="detail-value">{{ executionData.queryId || '-' }}</span>
          </div>
          <div v-if="executionData.status === 'error' && executionData.error" class="detail-item error-item">
            <label class="detail-label">错误原因:</label>
            <div class="error-container">
              <div class="error-content">{{ executionData.error }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 执行SQL -->
      <div class="detail-section">
        <h3 class="section-title">执行SQL</h3>
        <div class="sql-container">
          <pre class="sql-content">{{ executionData.executeSql }}</pre>
          <div class="sql-actions">
            <a-button size="small" @click="copySql">
              <template #icon>
                <CopyOutlined />
              </template>
              复制SQL
            </a-button>
          </div>
        </div>
      </div>

      <!-- 执行参数 -->
      <div v-if="executionData.parameters" class="detail-section">
        <h3 class="section-title">执行参数</h3>
        <div class="parameters-container">
          <pre class="parameters-content">{{ formatParameters(executionData.parameters) }}</pre>
          <div class="parameters-actions">
            <a-button size="small" @click="copyParameters">
              <template #icon>
                <CopyOutlined />
              </template>
              复制参数
            </a-button>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="detail-actions">
        <a-space>
          <a-button @click="handleCancel">
            关闭
          </a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import {
  CopyOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  LoadingOutlined,
  MinusCircleOutlined
} from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { EXECUTION_STATUS_CONFIG } from '@/types/execution-history';
import type { ExecutionHistory } from '@/types/execution-history';

// 组件属性
const props = defineProps<{
  visible: boolean;
  executionData: ExecutionHistory | null;
}>();

// 组件事件
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
}>();

// 响应式数据

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 处理取消
const handleCancel = () => {
  modalVisible.value = false;
};


// 复制SQL
const copySql = async () => {
  if (props.executionData?.executeSql) {
    try {
      await navigator.clipboard.writeText(props.executionData.executeSql);
      message.success('SQL已复制到剪贴板');
    } catch (error) {
      message.error('复制失败');
    }
  }
};

// 复制参数
const copyParameters = async () => {
  if (props.executionData?.parameters) {
    try {
      await navigator.clipboard.writeText(props.executionData.parameters);
      message.success('参数已复制到剪贴板');
    } catch (error) {
      message.error('复制失败');
    }
  }
};

// 工具函数
const getStatusColor = (status: string) => {
  return EXECUTION_STATUS_CONFIG[status as keyof typeof EXECUTION_STATUS_CONFIG]?.color || 'default';
};

const getStatusLabel = (status: string) => {
  return EXECUTION_STATUS_CONFIG[status as keyof typeof EXECUTION_STATUS_CONFIG]?.label || status;
};

const getStatusIcon = (status: string) => {
  const iconMap = {
    success: CheckCircleOutlined,
    error: CloseCircleOutlined,
    running: LoadingOutlined,
    cancelled: MinusCircleOutlined
  };
  return iconMap[status as keyof typeof iconMap] || CheckCircleOutlined;
};

const formatDateTime = (dateTime: string) => {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss');
};

const formatParameters = (parameters: string) => {
  try {
    const parsed = JSON.parse(parameters);
    return JSON.stringify(parsed, null, 2);
  } catch {
    return parameters;
  }
};
</script>

<style scoped>
.execution-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-label {
  font-weight: 500;
  color: #595959;
  min-width: 80px;
}

.detail-value {
  color: #262626;
  word-break: break-all;
}

.error-item {
  grid-column: 1 / -1; /* 占满整行 */
  align-items: flex-start;
  margin-top: 8px;
}

.error-item .detail-label {
  color: #ff4d4f;
  font-weight: 600;
  margin-top: 4px;
}

.error-container {
  flex: 1;
  background: linear-gradient(135deg, #fff2f0 0%, #ffe7e6 100%);
  border: 1px solid #ffccc7;
  border-radius: 8px;
  padding: 12px 16px;
  position: relative;
  box-shadow: 0 2px 4px rgba(255, 77, 79, 0.1);
}

.error-container::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: #ff4d4f;
  border-radius: 8px 0 0 8px;
}

.error-content {
  color: #d32f2f;
  font-size: 13px;
  line-height: 1.6;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 200px;
  overflow-y: auto;
  padding-right: 8px;
}

.error-content::-webkit-scrollbar {
  width: 6px;
}

.error-content::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

.error-content::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.error-content::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

.sql-container,
.parameters-container {
  position: relative;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
}

.sql-content,
.parameters-content {
  margin: 0;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #262626;
  background: transparent;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 300px;
  overflow-y: auto;
}

.sql-actions,
.parameters-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.sql-container:hover .sql-actions,
.parameters-container:hover .parameters-actions {
  opacity: 1;
}

.detail-actions {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .detail-label {
    min-width: auto;
  }
}
</style>
