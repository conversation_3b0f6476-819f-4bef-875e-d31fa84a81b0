import { defineStore } from 'pinia'
import { ref, computed, onUnmounted } from 'vue'
import type {
  DataSource,
  DataSourceType,
  DataSourceStatus,
  CreateDataSourceParams,
  UpdateDataSourceParams,
  DataSourceQueryParams,
  PageResponse
} from '@/types/datasource'
import { DataSourceTypeEnum, DataSourceStatusEnum } from '@/types/datasource'
import type { Metadata, TableMetadata, SchemaMetadata, ColumnMetadata, ExtendedTableMetadata } from '@/types/metadata'
import { dataSourceService } from '@/services/datasource'
import { message } from '@/services/message'
import { loading } from '@/services/loading'
import {
  useResponseHandler,
  adaptDataSourceListResponse,
  adaptDataSourceResponse
} from '@/utils/api'

export const useDataSourceStore = defineStore('dataSource', () => {
  // 状态
  const dataSources = ref<DataSource[]>([])
  const currentDataSource = ref<DataSource | null>(null)
  const pagination = ref({
    total: 0,
    page: 1,
    size: 10,
    totalPages: 0
  })
  const isLoading = ref(false)
  const error = ref<Error | null>(null)
  const isLoadingMetadata = ref(false)
  const isUpdateLoading = ref(false)
  const isUpdateError = ref(false)
  const updateError = ref<string | null>(null)
  const isCreateLoading = ref(false)
  const isCreateError = ref(false)
  const createError = ref<string | null>(null)

  // 元数据相关状态
  const metadataState = ref<{
    schemas: Map<string, SchemaMetadata[]>  // dataSourceId -> schemas
    tables: Map<string, TableMetadata[]>    // schemaId -> tables
    loading: {
      schemas: Set<string>  // 正在加载schema的数据源ID集合
      tables: Set<string>   // 正在加载表的schemaId集合
    }
    error: Map<string, {
      message: string
      timestamp: number
      retryCount: number
    }>  // 数据源ID -> 错误信息
    lastUpdate: Map<string, number>  // 数据源ID -> 最后更新时间戳
    syncProgress: Map<string, {
      total: number
      current: number
      status: 'idle' | 'syncing' | 'error' | 'completed'
      message: string
    }> // 数据源ID -> 同步进度
    cacheConfig: {
      ttl: number  // 缓存过期时间（毫秒）
      maxRetries: number  // 最大重试次数
      retryDelay: number  // 重试延迟（毫秒）
      batchSize: number   // 批量加载大小
    }
  }>({
    schemas: new Map(),
    tables: new Map(),
    loading: {
      schemas: new Set(),
      tables: new Set()
    },
    error: new Map(),
    lastUpdate: new Map(),
    syncProgress: new Map(),
    cacheConfig: {
      ttl: 5 * 60 * 1000,  // 默认5分钟
      maxRetries: 3,
      retryDelay: 1000,
      batchSize: 50
    }
  })

  // 元数据预加载配置
  const preloadConfig = ref({
    enabled: true,
    maxConcurrent: 3,
    preloadDepth: 1  // 预加载深度：0=不预加载，1=只预加载schemas，2=预加载schemas和tables
  })

  // 增量更新配置
  const incrementalUpdateConfig = ref({
    enabled: true,
    interval: 5 * 60 * 1000,  // 5分钟检查一次
    maxAge: 30 * 60 * 1000    // 30分钟后强制更新
  })

  // 性能监控
  const performanceMetrics = ref({
    loadTimes: new Map<string, number[]>(),    // 加载时间记录
    errorRates: new Map<string, number>(),     // 错误率
    cacheHitRates: new Map<string, number>(),  // 缓存命中率
    lastAnalysis: 0                            // 上次分析时间
  })

  // 计算属性
  const activeDataSources = computed(() => {
    return dataSources.value.filter(ds => ds.status === 'active')
  })

  const dataSourcesByType = computed(() => {
    const result: Record<DataSourceType, DataSource[]> = {
      'mysql': [],
      'postgresql': [],
      'oracle': [],
      'sqlserver': [],
      'mongodb': [],
      'elasticsearch': []
    }

    dataSources.value.forEach(ds => {
      result[ds.type].push(ds)
    })

    return result
  })

  // 查找类型为特定数据源类型的数据源
  const getDataSourcesByType = computed(() => {
    const result = {} as Record<DataSourceType, DataSource[]>;

    // 初始化每种类型的空数组
    ['mysql', 'postgresql', 'oracle', 'sqlserver', 'mongodb', 'elasticsearch'].forEach(type => {
      result[type as DataSourceType] = [];
    });

    // 按类型分组数据源
    dataSources.value.forEach(ds => {
      if (ds.type in result) {
        result[ds.type].push(ds);
      }
    });

    return result;
  });

  // 获取不同状态数据源的数量
  const getStatusCounts = computed(() => {
    const counts = {
      total: dataSources.value.length,
      active: 0,
      inactive: 0,
      error: 0,
      syncing: 0
    };

    dataSources.value.forEach(ds => {
      if (ds.status === 'active') {
        counts.active++;
      } else if (ds.status === 'inactive') {
        counts.inactive++;
      } else if (ds.status === 'error') {
        counts.error++;
      } else if (ds.status === 'syncing') {
        counts.syncing++;
      }
    });

    return counts;
  });

  // 获取数据源列表
  const fetchDataSources = async (params: DataSourceQueryParams = {}) => {
    isLoading.value = true
    error.value = null

    // 打印收到的参数
    console.log('[DataSourceStore] fetchDataSources 接收到的参数:', params);

    try {
      // 构建查询参数，确保只传递非空值
      const queryParams: DataSourceQueryParams = {
        page: params.page || pagination.value.page,
        size: params.size || pagination.value.size
      };

      // 只添加有值的过滤参数
      if (params.name) queryParams.name = params.name;
      if (params.type) queryParams.type = params.type;
      if (params.status) queryParams.status = params.status;

      console.log('[DataSourceStore] 发送到服务层的查询参数:', queryParams);

      const response = await dataSourceService.getDataSources(queryParams);

      // 使用通用适配器处理响应
      const pageResponse = adaptDataSourceListResponse(response)

      // 更新状态
      dataSources.value = pageResponse.items
      pagination.value = {
        total: pageResponse.total,
        page: pageResponse.page,
        size: pageResponse.size,
        totalPages: pageResponse.totalPages
      }
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err))
      message.error('加载数据源列表失败')
    } finally {
      isLoading.value = false
    }
  }

  // 获取单个数据源
  const getDataSourceById = async (id: string, forceRefresh: boolean = false) => {
    // 如果不强制刷新，且currentDataSource已经是请求的数据源，直接返回
    if (!forceRefresh && currentDataSource.value && currentDataSource.value.id === id) {
      console.log(`[DataSourceStore] 使用当前数据源(${id})，避免重复请求API`);
      return currentDataSource.value;
    }

    // 如果不强制刷新，且dataSources中已经有这个数据源，直接返回
    if (!forceRefresh) {
      const existingDataSource = dataSources.value.find(ds => ds.id === id);
      if (existingDataSource) {
        console.log(`[DataSourceStore] 从数据源列表获取数据源(${id})，避免请求API`);
        currentDataSource.value = existingDataSource;
        return existingDataSource;
      }
    }

    // 强制刷新或本地没有时，发起API请求
    console.log(`[DataSourceStore] 从API获取数据源(${id})${forceRefresh ? '（强制刷新）' : ''}`);
    isLoading.value = true;
    error.value = null;

    try {
      const response = await dataSourceService.getDataSource(id);
      const data = adaptDataSourceResponse(response);

      if (!data) {
        throw new Error(`未找到ID为${id}的数据源`);
      }

      currentDataSource.value = data;

      // 更新本地数据源列表中的对应项
      const index = dataSources.value.findIndex(ds => ds.id === id);
      if (index !== -1) {
        dataSources.value[index] = data;
      }

      return data;
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err));
      message.error('加载数据源详情失败');
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  // 创建数据源
  const createDataSource = async (params: CreateDataSourceParams) => {
    isCreateLoading.value = true
    isCreateError.value = false
    createError.value = null

    try {
      const response = await dataSourceService.createDataSource(params)
      // 添加到数据源列表
      dataSources.value.push(response)
      message.success('数据源创建成功')

      // 重新获取最新的数据源列表
      await fetchDataSources()

      return response
    } catch (error) {
      console.error('创建数据源失败:', error)
      isCreateError.value = true
      createError.value = error instanceof Error ? error.message : '未知错误'
      message.error(`创建数据源失败: ${createError.value}`)
      throw error
    } finally {
      isCreateLoading.value = false
    }
  }

  // 更新数据源
  const updateDataSource = async (params: UpdateDataSourceParams) => {
    isUpdateLoading.value = true
    isUpdateError.value = false
    updateError.value = null

    try {
      const response = await dataSourceService.updateDataSource(params)

      // 更新当前选中的数据源
      const index = dataSources.value.findIndex(ds => ds.id === params.id)
      if (index !== -1) {
        dataSources.value[index] = response
      }

      // 若当前选中的数据源就是更新的数据源，也更新currentDataSource
      if (currentDataSource.value && currentDataSource.value.id === params.id) {
        currentDataSource.value = response
      }

      // 重新获取最新的数据源列表
      await fetchDataSources()

      message.success('数据源更新成功')
      return response
    } catch (error) {
      console.error('更新数据源失败:', error)
      isUpdateError.value = true
      updateError.value = error instanceof Error ? error.message : '未知错误'
      message.error(`更新数据源失败: ${updateError.value}`)
      throw error
    } finally {
      isUpdateLoading.value = false
    }
  }

  // 删除数据源
  const deleteDataSource = async (id: string) => {
    isLoading.value = true
    error.value = null

    try {
      await dataSourceService.deleteDataSource(id)

      // 更新本地状态而不是发起新请求
      dataSources.value = dataSources.value.filter(ds => ds.id !== id)

      if (currentDataSource.value?.id === id) {
        currentDataSource.value = null
      }

      // 不再自动获取最新的数据源列表，避免多次请求
      // 返回值
      return true
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err))
      throw error // 将错误向上抛出，让调用者处理
    } finally {
      isLoading.value = false
    }
  }

  // 测试连接
  const testDataSourceConnection = async (params: any) => {
    try {
      loading.show('测试连接中...')
      let result;

      // 如果是已存在的数据源，使用testExistingConnection
      if (params.id) {
        result = await dataSourceService.testExistingConnection(params.id)
      } else {
        result = await dataSourceService.testConnection(params)
      }

      console.log('测试连接结果:', result);

      // 成功或失败取决于result.success
      if (result.success) {
        message.success('连接成功')
      } else {
        message.error(`连接失败: ${result.message}`)
      }

      return result
    } catch (err) {
      console.error('测试连接失败:', err);
      message.error('测试连接失败')
      return {
        success: false,
        message: err instanceof Error ? err.message : String(err)
      }
    } finally {
      loading.hide()
    }
  }

  // 同步元数据
  const syncDataSourceMetadata = async (id: string) => {
    // 检查ID是否有效
    if (!id || id === 'undefined') {
      console.error('同步元数据失败: 无效的数据源ID', id);
      return {
        success: false,
        message: '无效的数据源ID'
      };
    }

    try {
      loading.show('同步元数据中...')
      const result = await dataSourceService.syncMetadata({ id })
      console.log('元数据同步结果:', result)

      // 使用统一的响应处理器
      const { handleResponse } = useResponseHandler()
      handleResponse(result, {
        showSuccessMessage: true,
        successMessage: '元数据同步成功',
        errorMessage: '元数据同步失败'
      })

      if (result.success) {
        // 更新当前数据源的元数据
        if (currentDataSource.value?.id === id) {
          currentDataSource.value = await getDataSourceById(id)
        }

        // 更新列表中的数据源
        const index = dataSources.value.findIndex(ds => ds.id === id)
        if (index !== -1) {
          const updatedDataSource = await dataSourceService.getDataSource(id)
          dataSources.value[index] = updatedDataSource
        }

        // 刷新数据源列表
        await fetchDataSources()
      }

      return result
    } catch (err) {
      console.error('同步元数据失败:', err)
      message.error(`同步元数据失败: ${err instanceof Error ? err.message : String(err)}`)
      // 错误已由响应处理器处理
      return {
        success: false,
        message: err instanceof Error ? err.message : String(err)
      }
    } finally {
      loading.hide()
    }
  }

  // 获取数据源的元数据信息（表结构等）
  const getDataSourceMetadata = async (dataSourceId: string): Promise<Metadata | null> => {
    if (isLoadingMetadata.value) {
      console.warn('元数据加载中，请勿重复请求')
      return null
    }

    isLoadingMetadata.value = true

    try {
      // 首先尝试获取已有数据源，如果存在则直接加载其元数据
      const dataSource = dataSources.value.find(ds => ds.id === dataSourceId)

      if (dataSource && dataSource.metadata && dataSource.metadata.tables && dataSource.metadata.tables.length > 0) {
        console.log(`数据源 ${dataSourceId} 已有元数据`)
        isLoadingMetadata.value = false
        return dataSource.metadata
      }

      // 如果数据源不存在或无元数据，尝试同步
      console.log(`开始同步元数据，数据源ID: ${dataSourceId}`)

      let syncResult
      try {
        syncResult = await dataSourceService.syncMetadata({
          id: dataSourceId,
          filters: {
            includeSchemas: [],
            excludeSchemas: [],
            includeTables: [],
            excludeTables: []
          }
        })
        console.log('元数据同步结果:', syncResult)
      } catch (syncError) {
        console.error('同步元数据API返回错误:', syncError)

        // 同步出错时，不直接返回错误，而是尝试直接从API获取基础元数据
        try {
          console.log('尝试直接从API获取表列表...')
          const tablesResult = await dataSourceService.getTableMetadata(dataSourceId);

          // 验证结果是否为数组
          let tablesList: TableMetadata[] = [];

          if (tablesResult) {
            // 如果结果是数组，直接使用，否则创建单元素数组
            if (Array.isArray(tablesResult)) {
              tablesList = tablesResult as TableMetadata[];
            } else {
              // 单个表元数据，添加到数组中
              tablesList = [tablesResult as TableMetadata];
            }
          }

          if (tablesList.length > 0) {
            console.log(`直接获取到 ${tablesList.length} 个表的信息`);

            // 创建或更新数据源的元数据
            const metadata: Metadata = {
              tables: tablesList,
              lastSyncTime: new Date().toISOString()
            };

            // 更新数据源对象
            if (dataSource) {
              dataSource.metadata = metadata;

              // 更新数据源列表
              const index = dataSources.value.findIndex(ds => ds.id === dataSourceId);
              if (index !== -1) {
                dataSources.value[index] = dataSource;
              }
            }

            return metadata;
          }
        } catch (directError) {
          console.error('直接获取表信息失败:', directError);
        }

        // 如果上述方法都失败，则返回一个最小的元数据对象，避免UI报错
        const emptyMetadata: Metadata = {
          tables: [],
          lastSyncTime: new Date().toISOString()
        };
        return emptyMetadata;
      }

      // 同步成功后，重新获取数据源信息
      try {
        const updatedDataSource = await dataSourceService.getDataSource(dataSourceId);

        // 如果获取不到元数据，尝试直接获取表列表
        if (!updatedDataSource.metadata || !updatedDataSource.metadata.tables || updatedDataSource.metadata.tables.length === 0) {
          console.log('更新后的数据源仍然没有元数据，尝试直接获取表列表');
          try {
            // 调用API获取表列表，不传入表名参数
            const tablesResult = await dataSourceService.getTableMetadata(dataSourceId);

            // 验证结果是否为数组
            let tablesList: TableMetadata[] = [];

            if (tablesResult) {
              // 如果结果是数组，直接使用，否则创建单元素数组
              if (Array.isArray(tablesResult)) {
                tablesList = tablesResult as TableMetadata[];
              } else {
                // 单个表元数据，添加到数组中
                tablesList = [tablesResult as TableMetadata];
              }
            }

            // 更新元数据
            updatedDataSource.metadata = {
              tables: tablesList,
              lastSyncTime: new Date().toISOString()
            };
          } catch (error) {
            console.error('获取表列表失败:', error);
            updatedDataSource.metadata = {
              tables: [],
              lastSyncTime: new Date().toISOString()
            };
          }
        }

        // 更新数据源列表
        const index = dataSources.value.findIndex(ds => ds.id === dataSourceId);
        if (index !== -1) {
          dataSources.value[index] = updatedDataSource;
        }

        // 处理metadata可能为undefined的情况
        return updatedDataSource.metadata || null;
      } catch (getDataSourceError) {
        console.error('获取更新后的数据源信息失败:', getDataSourceError);

        // 返回一个最小的元数据对象，避免UI报错
        const emptyMetadata: Metadata = {
          tables: [],
          lastSyncTime: new Date().toISOString()
        };
        return emptyMetadata;
      }
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err));
      console.error('加载元数据失败:', err);

      // 检查错误是否与HTML响应有关
      const errorMessage = err instanceof Error ? err.message : String(err)
      if (errorMessage.includes('HTML而非JSON')) {
        console.error('API返回了HTML而非JSON，这通常表示会话已过期或服务器错误')

        // 创建更具体的错误
        error.value = new Error('会话可能已过期，请尝试重新登录')

        // 可以通过事件总线通知应用程序需要登录
        // eventBus.emit('session-expired')
      }

      // 返回一个最小的元数据对象，避免UI报错
      const emptyMetadata: Metadata = {
        tables: [],
        lastSyncTime: new Date().toISOString()
      };
      return emptyMetadata;
    } finally {
      isLoadingMetadata.value = false;
    }
  }

  // 获取表的元数据
  const getTableMetadata = (dataSourceId: string, tableName: string): TableMetadata | null => {
    const ds = dataSources.value.find(d => d.id === dataSourceId)

    if (!ds || !ds.metadata || !ds.metadata.tables) {
      return null
    }

    return ds.metadata.tables.find(t => t.name === tableName) || null
  }

  // 搜索表和列
  const searchMetadata = (dataSourceId: string, term: string): { tables: TableMetadata[], columns: { table: string, column: string }[] } => {
    const result = {
      tables: [] as TableMetadata[],
      columns: [] as { table: string, column: string }[]
    }

    if (!term) return result

    const ds = dataSources.value.find(d => d.id === dataSourceId)

    if (!ds || !ds.metadata || !ds.metadata.tables) {
      return result
    }

    const searchTerm = term.toLowerCase()

    // 搜索表
    result.tables = ds.metadata.tables.filter(table =>
      table.name.toLowerCase().includes(searchTerm)
    )

    // 搜索列
    ds.metadata.tables.forEach(table => {
      table.columns.forEach(column => {
        if (column.name.toLowerCase().includes(searchTerm)) {
          result.columns.push({
            table: table.name,
            column: column.name
          })
        }
      })
    })

    return result
  }

  // 新增的元数据管理方法
  const clearMetadataCache = (dataSourceId: string) => {
    console.log(`[DataSourceStore] 清除数据源 ${dataSourceId} 的元数据缓存`)
    metadataState.value.schemas.delete(dataSourceId)
    // 清除相关的表缓存
    const schemasToRemove = Array.from(metadataState.value.tables.keys())
      .filter(key => key.startsWith(`${dataSourceId}:`))
    schemasToRemove.forEach(key => metadataState.value.tables.delete(key))
    metadataState.value.error.delete(dataSourceId)
    metadataState.value.lastUpdate.delete(dataSourceId)
    metadataState.value.syncProgress.delete(dataSourceId)
  }

  // 检查缓存是否过期
  const isCacheExpired = (lastUpdate: number | undefined): boolean => {
    if (!lastUpdate) return true
    const now = Date.now()
    return now - lastUpdate > metadataState.value.cacheConfig.ttl
  }

  // 更新同步进度
  const updateSyncProgress = (dataSourceId: string, progress: {
    total: number
    current: number
    status: 'idle' | 'syncing' | 'error' | 'completed'
    message: string
  }) => {
    metadataState.value.syncProgress.set(dataSourceId, progress)
  }

  // 获取同步进度
  const getSyncProgress = (dataSourceId: string) => {
    return metadataState.value.syncProgress.get(dataSourceId) || {
      total: 0,
      current: 0,
      status: 'idle',
      message: ''
    }
  }

  // 带重试机制的API调用
    const retryableApiCall = async <T>(
        apiCall: () => Promise<T>,
        errorHandler: (error: any) => void
    ): Promise<T> => {
        try {
            return await apiCall()
        } catch (error) {
            console.warn(`API调用失败`, error)
            errorHandler(error)
            throw error
        }
    }

  // 优化的获取schemas方法
  const getSchemas = async (dataSourceId: string): Promise<SchemaMetadata[]> => {
    console.log(`[DataSourceStore] 获取数据源 ${dataSourceId} 的schemas`)

    // 检查缓存
    const cached = metadataState.value.schemas.get(dataSourceId)
    const lastUpdate = metadataState.value.lastUpdate.get(dataSourceId)

    // 如果缓存存在且未过期
    if (cached && !isCacheExpired(lastUpdate)) {
      console.log(`[DataSourceStore] 使用缓存的schemas数据，共 ${cached.length} 个`)
      return cached
    }

    // 设置加载状态
    metadataState.value.loading.schemas.add(dataSourceId)

    try {
      const schemas = await retryableApiCall(
        async () => {
          const response = await dataSourceService.getSchemas(dataSourceId)

          // 处理API返回的schemas数据
          console.log('[DataSourceStore] 原始API返回的schemas数据:', JSON.stringify(response, null, 2));

          // 检查返回的数据结构，确保我们能够正确处理不同格式的响应
          return response.map((schemaItem: any) => {
            // 如果API返回的是字符串类型，创建一个基本的schema对象
            if (typeof schemaItem === 'string') {
              return {
                id: schemaItem,     // 保留原始字符串作为ID
                value: schemaItem,  // 使用字符串当作值
                name: schemaItem,   // 使用字符串当作名称
                dataSourceId,       // 关联的数据源ID
                tablesCount: 0      // 默认表格数量为0
              }
            } else if (typeof schemaItem === 'object' && schemaItem !== null) {
              // 如果API返回的是对象，直接使用API的对象结构
              const result = {
                ...schemaItem,            // 保留原始对象的所有属性
                value: schemaItem.id,     // 使用原始id作为value
                dataSourceId              // 添加dataSourceId
              };
              console.log(`[DataSourceStore] 处理后的schema对象: ${JSON.stringify(result, null, 2)}`);
              return result;
            }

            // 当schema既不是字符串也不是非空对象时，不设置默认值
            return null
          });
        },
        (error) => {
          const errorInfo = {
            message: error instanceof Error ? error.message : String(error),
            timestamp: Date.now(),
            retryCount: 0
          }
          metadataState.value.error.set(dataSourceId, errorInfo)
        }
      )

      // 更新缓存
      metadataState.value.schemas.set(dataSourceId, schemas)
      metadataState.value.lastUpdate.set(dataSourceId, Date.now())

      console.log(`[DataSourceStore] 成功获取到 ${schemas.length} 个schemas，数据:`, schemas)
      return schemas
    } finally {
      metadataState.value.loading.schemas.delete(dataSourceId)
    }
  }

  // 优化的获取表方法
  const getTables = async (dataSourceId: string, schemaId: string): Promise<TableMetadata[]> => {
    console.log(`[DataSourceStore] 获取schema ${schemaId} 的表`)

    const cacheKey = `${dataSourceId}:${schemaId}`

    // 检查缓存
    const cached = metadataState.value.tables.get(cacheKey)
    const lastUpdate = metadataState.value.lastUpdate.get(cacheKey)

    // 如果缓存存在且未过期
    if (cached && !isCacheExpired(lastUpdate)) {
      console.log(`[DataSourceStore] 使用缓存的表数据，共 ${cached.length} 个`)
      return cached
    }

    // 设置加载状态
    metadataState.value.loading.tables.add(cacheKey)

    try {
      const tables = await retryableApiCall(
        async () => {
          const result = await dataSourceService.getTables(schemaId)

          // 处理分页
          if (result.length > metadataState.value.cacheConfig.batchSize) {
            console.log(`[DataSourceStore] 表数量超过批次大小，使用分页加载`)
            const batches = Math.ceil(result.length / metadataState.value.cacheConfig.batchSize)

            // 更新同步进度
            updateSyncProgress(dataSourceId, {
              total: result.length,
              current: 0,
              status: 'syncing',
              message: '正在加载表信息...'
            })

            const allTables: TableMetadata[] = []

            for (let i = 0; i < batches; i++) {
              const start = i * metadataState.value.cacheConfig.batchSize
              const end = start + metadataState.value.cacheConfig.batchSize
              const batch = result.slice(start, end)

              // 获取每个表的详细信息
              const batchDetails = await Promise.all(
                batch.map(table => dataSourceService.getTableDetails(table.id))
              )

              allTables.push(...batchDetails)

              // 更新进度
              updateSyncProgress(dataSourceId, {
                total: result.length,
                current: allTables.length,
                status: 'syncing',
                message: `已加载 ${allTables.length}/${result.length} 个表`
              })
            }

            // 完成加载
            updateSyncProgress(dataSourceId, {
              total: result.length,
              current: result.length,
              status: 'completed',
              message: `成功加载 ${result.length} 个表`
            })

            return allTables
          }

          return result
        },
        (error) => {
          const errorInfo = {
            message: error instanceof Error ? error.message : String(error),
            timestamp: Date.now(),
            retryCount: 0
          }
          metadataState.value.error.set(cacheKey, errorInfo)
        }
      )

      // 更新缓存
      metadataState.value.tables.set(cacheKey, tables)
      metadataState.value.lastUpdate.set(cacheKey, Date.now())

      console.log(`[DataSourceStore] 成功获取到 ${tables.length} 个表`)
      return tables
    } finally {
      metadataState.value.loading.tables.delete(cacheKey)
    }
  }

  // 计算属性：获取数据源的加载状态
  const isMetadataLoading = computed(() => (dataSourceId: string) => {
    return metadataState.value.loading.schemas.has(dataSourceId) ||
           Array.from(metadataState.value.loading.tables.keys())
             .some(key => key.startsWith(`${dataSourceId}:`))
  })

  // 计算属性：获取数据源的错误信息
  const getMetadataError = computed(() => (dataSourceId: string) => {
    return metadataState.value.error.get(dataSourceId)
  })

  // 保存表数据到缓存 - 简化实现
  const saveTablesToCache = (dataSourceId: string, tables: TableMetadata[] | ExtendedTableMetadata[], schema?: string) => {
    if (!dataSourceId || !tables || !Array.isArray(tables)) {
      console.warn(`[DataSourceStore] 保存表数据到缓存失败: 参数无效`);
      return;
    }

    try {
      // 确定缓存键
      const cacheKey = schema ? `${dataSourceId}:${schema}` : `${dataSourceId}:`;
      console.log(`[DataSourceStore] 保存${tables.length}张表到缓存, 键: ${cacheKey}`);

      // 只保存核心表数据，不做复杂转换
      metadataState.value.tables.set(cacheKey, [...tables]);
      metadataState.value.lastUpdate.set(cacheKey, Date.now());

      console.log(`[DataSourceStore] 成功保存${tables.length}张表到缓存`);
    } catch (error) {
      console.error(`[DataSourceStore] 保存表数据到缓存出错:`, error);
    }
  };

  return {
    // 状态
    dataSources,
    currentDataSource,
    pagination,
    isLoading,
    error,
    isLoadingMetadata,
    isUpdateLoading,
    isUpdateError,
    updateError,
    isCreateLoading,
    isCreateError,
    createError,

    // 计算属性
    activeDataSources,
    dataSourcesByType,
    getDataSourcesByType,
    getStatusCounts,

    // 方法
    fetchDataSources,
    getDataSourceById,
    createDataSource,
    updateDataSource,
    deleteDataSource,
    testDataSourceConnection,
    syncDataSourceMetadata,
    getDataSourceMetadata,
    getTableMetadata,
    searchMetadata,

    // 获取表数据预览
    getTableDataPreview: async (
      dataSourceId: string,
      tableName: string,
      params: {
        page?: number,
        size?: number,
        sort?: string,
        order?: 'asc' | 'desc',
        filters?: Record<string, any>
      } = {}
    ) => {
      try {
        return await dataSourceService.getTableDataPreview(
          dataSourceId,
          tableName,
          params
        )
      } catch (err) {
        error.value = err instanceof Error ? err : new Error(String(err))
        throw error.value
      }
    },

    // 高级搜索
    advancedSearch: async (
      params: {
        keyword: string,
        dataSourceIds: string[],
        entityTypes: ('table' | 'column' | 'view')[],
        caseSensitive?: boolean,
        page?: number,
        size?: number
      }
    ) => {
      try {
        return await dataSourceService.advancedSearch(params)
      } catch (err) {
        error.value = err instanceof Error ? err : new Error(String(err))
        throw error.value
      }
    },

    // 新增返回值
    clearMetadataCache,
    getSchemas,
    getTables,
    isMetadataLoading,
    getMetadataError,
    metadataState: computed(() => metadataState.value),

    getTablesFromCache: (dataSourceId: string, schema?: string): TableMetadata[] | null => {
      // 如果提供了schema，尝试获取该schema下的表
      if (schema) {
        const schemaKey = `${dataSourceId}:${schema}`;
        const cachedTables = metadataState.value.tables.get(schemaKey);
        return cachedTables || null;
      }

      // 如果没有提供schema，尝试查找任何属于该数据源的表
      // 这种情况通常出现在无schema的数据源
      const schemalessKey = `${dataSourceId}:`;
      let cachedTables = metadataState.value.tables.get(schemalessKey);

      // 如果没有找到无schema的缓存，尝试在所有缓存中查找匹配dataSourceId的项
      if (!cachedTables) {
        for (const [key, tables] of metadataState.value.tables.entries()) {
          if (key.startsWith(`${dataSourceId}:`)) {
            cachedTables = tables;
            break;
          }
        }
      }

      return cachedTables || null;
    },
    saveTablesToCache
  }
})
