import { ref, watch } from 'vue';
import { useQueryStore } from '@/stores/query';
import { message } from 'ant-design-vue';

/**
 * 查询执行相关逻辑的组合式API
 */
export function useQueryExecution() {
  // Store
  const queryStore = useQueryStore();
  
  // 状态
  const isExecuting = ref(false);
  const executionTime = ref(0);
  const executionTimer = ref<number | null>(null);
  const queryError = ref<string | null>(null);
  const statusMessage = ref<string | null>(null);
  
  // 组件状态检查
  let isComponentMounted = true;
  
  /**
   * 模拟延迟
   * @param min 最小延迟(ms)
   * @param max 最大延迟(ms)
   */
  const simulateDelay = (min: number, max: number): Promise<void> => {
    const delay = min + Math.random() * (max - min);
    return new Promise(resolve => setTimeout(resolve, delay));
  };
  
  /**
   * 执行查询
   * @param params 查询参数 (dataSourceId, queryText, queryType, parameters)
   */
  const executeQuery = async (params: {
    dataSourceId: string;
    queryText?: string;
    sql?: string;
    queryType: string;
    parameters?: Record<string, any>;
    versionId?: string;
  }) => {
    // 检查组件状态
    if (!isComponentMounted) return;
    
    // 检查是否正在执行
    if (isExecuting.value) return;
    
    try {
      console.log('composables/useQueryExecution开始执行查询，参数:', {
        dataSourceId: params.dataSourceId,
        queryText长度: params.queryText?.length || 0,
        sql长度: params.sql?.length || 0,
        queryType: params.queryType,
        hasParameters: params.parameters ? Object.keys(params.parameters).length : 0,
        versionId: params.versionId
      });
      
      // 验证数据源和查询内容
      if (!params.dataSourceId) {
        throw new Error('请选择数据源');
      }
      
      // 获取SQL文本，优先使用sql字段，兼容旧的queryText字段
      const queryText = params.sql || params.queryText;
      
      // 打印实际使用的查询文本，帮助诊断
      console.log('实际使用的查询文本:', {
        长度: queryText?.length || 0,
        预览: queryText?.substring(0, 100) || '',
        来源: params.sql ? 'sql参数' : 'queryText参数'
      });
      
      if (!queryText || queryText.trim() === '') {
        throw new Error(`请输入${params.queryType === 'SQL' ? 'SQL' : '自然语言'}查询内容`);
      }
      
      // 重置错误状态
      queryError.value = null;
      
      // 设置执行状态
      isExecuting.value = true;
      statusMessage.value = '正在执行查询...';
      
      // 启动计时器
      executionTime.value = 0;
      executionTimer.value = window.setInterval(() => {
        executionTime.value++;
      }, 1000);
      
      // 构造查询参数 - 确保键名符合API要求
      const queryParams: any = {
        dataSourceId: params.dataSourceId,
        queryType: params.queryType,
      };
      
      // 根据查询类型，将查询文本放到正确的字段
      if (params.queryType === 'SQL') {
        queryParams.sql = queryText; // 使用 sql 而不是 sqlText 或 queryText
        console.log('SQL查询文本长度:', queryText.length);
      } else {
        queryParams.question = queryText;
      }
      
      // 如果有参数且非空，添加到parameters字段
      if (params.parameters && Object.keys(params.parameters).length > 0) {
        queryParams.parameters = params.parameters;
      }
      
      // 如果有真实的版本ID（不是'default'），才添加
      if (params.versionId && params.versionId !== 'default') {
        queryParams.versionId = params.versionId;
      }
      
      console.log('调用查询API，参数:', queryParams);
      
      // 调用查询接口
      const result = await queryStore.executeQuery(queryParams);
      
      // 检查组件状态
      if (!isComponentMounted) return;
      
      if (result) {
        if (result.error) {
          // 查询执行失败
          queryError.value = result.error;
          statusMessage.value = `查询执行失败: ${result.error}`;
        } else {
          // 查询执行成功
          statusMessage.value = `查询执行成功，返回 ${result.rowCount} 条记录`;
        }
      } else {
        throw new Error('查询执行失败：未收到响应');
      }
    } catch (error) {
      console.error('执行查询失败:', error);
      
      // 设置错误信息
      const errorMessage = error instanceof Error ? error.message : '执行查询失败';
      queryError.value = errorMessage;
      statusMessage.value = errorMessage;
    } finally {
      // 检查组件状态
      if (!isComponentMounted) return;
      
      // 停止计时器
      if (executionTimer.value) {
        clearInterval(executionTimer.value);
        executionTimer.value = null;
      }
      
      // 重置执行状态
      isExecuting.value = false;
      
      // 3秒后清除状态消息
      setTimeout(() => {
        if (!isComponentMounted) return;
        statusMessage.value = null;
      }, 3000);
    }
  };
  
  /**
   * 取消查询
   * @param currentQueryId 当前查询ID
   */
  const cancelQuery = async (currentQueryId: string | null) => {
    if (!isExecuting.value) return;
    
    try {
      statusMessage.value = '正在取消查询...';
      
      // 模拟网络延迟
      await simulateDelay(800, 1500);
      
      // 如果有查询ID，尝试通过store取消
      if (currentQueryId) {
        await queryStore.cancelQuery(currentQueryId);
      }
      
      // 即使没有currentQueryId，也要强制取消当前执行状态
      isExecuting.value = false;
      
      // 清除执行时间计时器
      if (executionTimer.value) {
        clearInterval(executionTimer.value);
        executionTimer.value = null;
      }
      
      // 更新状态信息
      statusMessage.value = '查询已取消';
      
      // 添加模拟的错误消息
      queryError.value = '查询已被用户取消';
      
      setTimeout(() => {
        statusMessage.value = null;
      }, 3000);
    } catch (error) {
      console.error('取消查询失败:', error);
      statusMessage.value = '取消查询失败';
      
      // 即使取消失败，也要强制取消执行状态
      isExecuting.value = false;
      
      // 清除执行时间计时器
      if (executionTimer.value) {
        clearInterval(executionTimer.value);
        executionTimer.value = null;
      }
      
      setTimeout(() => {
        statusMessage.value = null;
      }, 5000);
    }
  };
  
  /**
   * 导出查询结果
   * @param format 导出格式
   */
  const exportResults = (format: 'excel') => {
    if (!queryStore.currentQueryResult) return;
    
    statusMessage.value = `正在导出为${format.toUpperCase()}...`;
    queryStore.exportQueryResults(queryStore.currentQueryResult.id, format);
    
    setTimeout(() => {
      statusMessage.value = null;
    }, 3000);
  };
  
  // 组件卸载时清理
  const cleanup = () => {
    isComponentMounted = false;
    if (executionTimer.value) {
      clearInterval(executionTimer.value);
      executionTimer.value = null;
    }
  };
  
  return {
    // 状态
    isExecuting,
    executionTime,
    queryError,
    statusMessage,
    
    // 方法
    executeQuery,
    cancelQuery,
    exportResults,
    simulateDelay,
    cleanup,
    
    // Store
    queryStore
  };
}