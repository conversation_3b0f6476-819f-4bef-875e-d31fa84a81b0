import { ref, computed } from 'vue';
import { message, Modal } from 'ant-design-vue';
import axios from 'axios';
import type { QueryVersion } from '@/types/queryVersion';
import { useRouter } from 'vue-router';
import { versionService } from '@/services/queryVersion';
import { getApiUrl } from '@/services/apiUtils';
import {getApiBaseUrl} from "@/services/query";
import instance from "@/utils/axios";

/**
 * 版本管理相关状态和方法
 */
export interface QueryVersionState {
  /** 版本状态 */
  versionStatus: ReturnType<typeof ref<'DRAFT' | 'PUBLISHED' | 'DEPRECATED'>>;
  /** 是否为当前活动版本 */
  isActiveVersion: ReturnType<typeof ref<boolean>>;
  /** 可用版本列表 */
  availableVersions: ReturnType<typeof ref<string[]>>;
  /** 当前选中的版本 */
  selectedVersion: ReturnType<typeof ref<string>>;
  /** 当前最高版本号 */
  currentMaxVersionNumber: ReturnType<typeof ref<number>>;
  /** 版本号 */
  queryVersion: ReturnType<typeof ref<string>>;
  /** 发布时间 */
  publishedAt: ReturnType<typeof ref<string | null>>;
  /** 最后编辑时间 */
  lastEditedAt: ReturnType<typeof ref<string | null>>;
  /** 废弃时间 */
  deprecatedAt: ReturnType<typeof ref<string | null>>;

  /** 创建新版本 */
  createNewVersion: () => Promise<void>;
  /** 发布版本 */
  publishVersion: (options: PublishVersionOptions) => Promise<void>;
  /** 加载查询版本 */
  loadVersions: (queryId: string) => Promise<void>;
}

/**
 * 发布版本选项
 */
export interface PublishVersionOptions {
  /** 查询ID */
  queryId: string;
  /** 查询文本 */
  queryText: string;
  /** 查询类型 */
      queryType: 'SQL' | 'natural-language';
  /** 查询名称 */
  queryName: string;
  /** 数据源ID */
  dataSourceId: string;
}

/**
 * 创建版本选项
 */
export interface CreateVersionOptions {
  /** 查询ID */
  queryId: string;
  /** 查询文本 */
  queryText: string;
  /** 数据源ID */
  dataSourceId: string;
  /** 描述 */
  description?: string;
}

/**
 * 模拟延时函数
 */
const simulateDelay = (min: number, max: number) => {
  const delay = Math.floor(Math.random() * (max - min + 1)) + min;
  return new Promise(resolve => setTimeout(resolve, delay));
};

/**
 * 查询版本管理的Composable函数
 */
export function useQueryVersion(): QueryVersionState {
  // 版本状态
  const versionStatus = ref<'DRAFT' | 'PUBLISHED' | 'DEPRECATED'>('DRAFT');
  const isActiveVersion = ref(false);
  const availableVersions = ref<string[]>(['V1']);
  const selectedVersion = ref('V1');
  const currentMaxVersionNumber = ref(1);
  const queryVersion = ref('V1');

  // 时间相关
  const publishedAt = ref<string | null>(null);
  const lastEditedAt = ref<string | null>(null);
  const deprecatedAt = ref<string | null>(null);

  // 状态消息
  const statusMessage = ref<string | null>(null);

  /**
   * 创建新版本
   */
  const createNewVersion = async () => {
    const queryId = '';
    if (!queryId) return;

    try {
      statusMessage.value = '正在创建新版本...';

      let success = false;
      let newVersionNumber = 0;

      try {
        // 尝试调用实际API
        console.log('尝试创建新版本，当前最高版本号:', currentMaxVersionNumber.value);
        // 计算新版本号 - 当前最高版本号加1
        newVersionNumber = currentMaxVersionNumber.value + 1;

        // 调用版本服务的创建版本API
        let versionService;
        try {
          const { versionService: importedService } = await import('@/services/queryVersion');
          versionService = importedService;

          if (versionService && versionService.createVersion) {
            // 构造参数 - 这里需要外部传入实际参数
            const createParams = {
              queryId: queryId,
              sqlContent: '',
              dataSourceId: ''
            };

            // 调用API
            const result = await versionService.createVersion(createParams);
            console.log('创建版本API返回结果:', result);

            if (result && result.versionNumber) {
              newVersionNumber = result.versionNumber;
              success = true;
            }
          }
        } catch (importError) {
          console.warn('无法导入queryVersion服务:', importError);

          // 如果无法导入服务，模拟成功
          await simulateDelay(800, 1500);
          success = true;
          console.log('使用前端模拟，创建新版本号:', newVersionNumber);
        }
      } catch (apiError) {
        console.warn('调用后端API失败，使用前端模拟:', apiError);

        // 模拟延迟
        await simulateDelay(500, 1000);

        // 在API调用失败的情况下，我们仍然模拟操作成功
        success = true;
        newVersionNumber = currentMaxVersionNumber.value + 1;

        // 显示调试模式提示信息
        message.warning({
          content: '后端API不可用',
          description: '使用前端模拟创建了新版本',
          duration: 5
        });
      }

      if (success) {
        // 更新版本状态
        versionStatus.value = 'DRAFT';
        isActiveVersion.value = false;
        lastEditedAt.value = new Date().toISOString();

        // 更新最高版本号
        currentMaxVersionNumber.value = newVersionNumber;

        // 更新版本号显示
        queryVersion.value = `V${newVersionNumber}`;
        selectedVersion.value = `V${newVersionNumber}`;

        // 更新可用版本列表
        if (!availableVersions.value.includes(`V${newVersionNumber}`)) {
          availableVersions.value.push(`V${newVersionNumber}`);
          // 确保版本号按数字排序
          sortVersions();
        }

        message.success({
          content: '版本创建成功',
          description: `已成功创建新版本 V${newVersionNumber}`,
          duration: 3
        });
      } else {
        throw new Error('创建新版本失败');
      }
    } catch (error) {
      console.error('创建新版本失败:', error);
      message.error({
        content: '创建新版本失败',
        description: error instanceof Error ? error.message : String(error),
        duration: 5
      });
    }
  };

  /**
   * 排序版本列表
   */
  const sortVersions = () => {
    availableVersions.value.sort((a, b) => {
      const numA = parseInt(a.slice(1));
      const numB = parseInt(b.slice(1));
      return numB - numA; // 降序排列
    });
  };

  /**
   * 发布版本
   */
  const publishVersion = async (options: PublishVersionOptions) => {
    if (!options.queryId || versionStatus.value !== 'DRAFT') return;

    try {
      statusMessage.value = '正在发布版本...';

      // 先调用前置接口检查 - 发布新版本的前置检查
      try {
        const { versionService } = await import('@/services/versionService');
        
        // 获取查询详情以获取 schema 信息
        const queryResponse = await instance.get(`/api/queries/${options.queryId}`);
        if (!queryResponse.success || !queryResponse.data) {
          throw new Error('获取查询详情失败');
        }
        
        const query = queryResponse.data;
        console.log('查询详情数据:', query);
        
        // 尝试从多个字段获取 schema 信息
        let schemaId = '';
        
        // 优先从 currentVersion 节点获取 schemaId
        if (query.currentVersion && query.currentVersion.schemaId) {
          schemaId = query.currentVersion.schemaId;
          console.log('从 currentVersion.schemaId 获取到 schemaId:', schemaId);
        }
        // 其次从查询详情根级别获取
        else if (query.schemaId || query.schema) {
          schemaId = query.schemaId || query.schema;
          console.log('从查询详情根级别获取到 schemaId:', schemaId);
        }
        
        // 如果还是没有，尝试从数据源信息中获取
        if (!schemaId && options.dataSourceId) {
          try {
            // 获取数据源详情
            const dataSourceResponse = await instance.get(`/api/datasources/${options.dataSourceId}`);
            if (dataSourceResponse.success && dataSourceResponse.data) {
              const dataSource = dataSourceResponse.data;
              console.log('数据源详情:', dataSource);
              
              // 尝试从数据源信息中获取默认 schema
              schemaId = dataSource.defaultSchema || dataSource.schemaId || dataSource.schema || '';
              
              // 如果还是没有，尝试获取数据源的第一个 schema
              if (!schemaId && dataSource.schemas && dataSource.schemas.length > 0) {
                schemaId = dataSource.schemas[0].id || dataSource.schemas[0].name || '';
              }
            }
          } catch (dsError) {
            console.warn('获取数据源详情失败，使用默认 schema:', dsError);
          }
        }
        
        console.log('最终使用的 schemaId:', schemaId);
        
        console.log('发布新版本前置检查参数:', {
          queryId: options.queryId,
          dataSourceId: options.dataSourceId,
          schemaId,
          sql: options.queryText
        });
        
        const isExposedInYop = await versionService.checkPublishNewVersionBeforehand(
          options.queryId,
          options.dataSourceId,
          schemaId,
          options.queryText
        );
        console.log('发布新版本前置检查结果 - 是否已在yop网关暴露:', isExposedInYop);

        if (isExposedInYop) {
          // 如果已在yop网关暴露，显示确认对话框
          const confirmed = await new Promise<boolean>((resolve) => {
            Modal.confirm({
              title: '发布确认',
              content: '此查询关联的集成配置已在yop网关暴露，修改可能会对此产生影响，是否继续发布？',
              okText: '继续发布',
              cancelText: '取消',
              onOk: () => resolve(true),
              onCancel: () => resolve(false),
            });
          });

          if (!confirmed) {
            statusMessage.value = null;
            // 用户取消，直接返回，不执行发布流程
            return { success: false, message: '用户取消发布' };
          }
        }
      } catch (error) {
        console.error('发布新版本前置检查失败:', error);
        // 如果前置检查失败，也直接返回，不执行发布流程
        statusMessage.value = '前置检查失败，无法发布';
        return;
      }

      // 查询当前草稿版本
      const versionApi = axios.create({
        baseURL: '', // 设置为空，让拦截器处理
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      // 检查是否使用模拟数据
      const USE_MOCK = import.meta.env.VITE_USE_MOCK_API === 'true';

      // 获取查询详情，查看是否有草稿版本 - 使用/api前缀
      const apiBaseUrl = '';  // 不使用getApiUrl()，直接使用相对路径
      console.log(`获取查询详情: /api/queries/${options.queryId}，模拟模式: ${USE_MOCK ? '是' : '否'}`);
      const queryResponse = await instance.get(`/api/queries/${options.queryId}`);
      console.log('获取查询详情-aaaa', queryResponse)
      if (!queryResponse.success || !queryResponse.data) {
        throw new Error('获取查询详情失败');
      }

      const query = queryResponse.data;
      console.log('查询详情:', query);

      // 如果没有草稿版本，先创建一个
      let draftVersionId = query.draftVersionId;

      if (!draftVersionId) {
        console.log('没有草稿版本，创建新版本...');

        try {
          // 创建草稿版本 - 使用/api前缀
          const apiUrl = `/api/queries/${options.queryId}/versions`;

          console.log(`创建查询草稿版本: ${apiUrl}，模拟模式: ${USE_MOCK ? '是' : '否'}`);

          // 发起版本创建请求
          const createResponse = await instance.post(apiUrl, {
            sqlContent: options.queryText,
            dataSourceId: options.dataSourceId,
            description: query.description || ''
          });

          if (!createResponse.success || !createResponse.data) {
            console.error('创建版本响应数据:', createResponse.data);
            throw new Error(createResponse.data?.message || '创建版本失败');
          }

          draftVersionId = createResponse.data.id;
          console.log('草稿版本创建成功:', draftVersionId, createResponse.data);
        } catch (createError) {
          console.error('创建草稿版本失败:', createError);
          throw new Error(`创建草稿版本失败: ${createError instanceof Error ? createError.message : String(createError)}`);
        }
      }

      // 发布版本 - 使用/api前缀
      try {
        const publishUrl = `/api/queries/versions/${draftVersionId}/publish`;

        console.log(`发布版本: ${publishUrl}，模拟模式: ${USE_MOCK ? '是' : '否'}`);
        const publishResponse = await instance.post(publishUrl);

        // 适配后端统一的API响应格式
        if (!publishResponse.data || !publishResponse.success) {
          console.error('发布响应数据:', publishResponse.data);
          const errorMsg = publishResponse?.message || publishResponse?.error?.message || '发布版本失败';
          throw new Error(errorMsg);
        }

        // 从统一的success/data格式中获取版本数据
        const publishedVersion = publishResponse.data;
        if (!publishedVersion || (!publishedVersion.status && !publishedVersion.versionStatus) ||
            (publishedVersion.status !== 'PUBLISHED' && publishedVersion.versionStatus !== 'PUBLISHED')) {
          console.error('发布版本数据格式不正确:', publishedVersion);
          throw new Error('发布版本失败: 响应数据不符合预期格式');
        }

        console.log('版本发布成功:', publishedVersion);
      } catch (publishError) {
        console.error('发布版本失败:', publishError);
        throw new Error(`发布版本失败: ${publishError instanceof Error ? publishError.message : String(publishError)}`);
      }

      // 激活版本 - 使用/api前缀
      try {
        const activateUrl = `/api/queries/${query.id}/versions/${draftVersionId}/activate`;

        console.log(`激活版本: ${activateUrl}，模拟模式: ${USE_MOCK ? '是' : '否'}`);
        const activateResponse = await instance.post(activateUrl);

        // 适配后端统一的API响应格式
        if (!activateResponse) {
          console.error('激活响应数据:', activateResponse);
          throw new Error('激活版本失败: 返回数据不符合预期格式');
        }

        // 兼容不同格式的响应数据
        let activatedData = activateResponse.data;

        // 处理可能的包装响应结构
        if (activateResponse.success && activateResponse.data) {
          console.log('从success/data统一响应格式中获取数据');
          activatedData = activateResponse.data;
        }

        console.log('处理后的激活数据:', activatedData);

        // 验证数据有效性
        if (!activatedData || (typeof activatedData === 'object' && !Object.keys(activatedData).length)) {
          console.error('激活数据无效:', activatedData);
          throw new Error('激活版本失败: 响应数据为空');
        }

        // 确认数据中包含必要的信息
        if (typeof activatedData === 'object' && !activatedData.id && !activatedData.queryId) {
          console.error('激活数据格式不正确:', activatedData);
          throw new Error('激活版本失败: 响应数据不包含必要信息');
        }

        console.log('版本激活成功:', activatedData);

        // 更新版本状态
        versionStatus.value = 'PUBLISHED';
        isActiveVersion.value = true;
        publishedAt.value = new Date().toISOString();
      } catch (activateError) {
        console.error('激活版本失败:', activateError);
        throw new Error(`激活版本失败: ${activateError instanceof Error ? activateError.message : String(activateError)}`);
      }

      // 只有在真正发布成功后才显示成功消息
      message.success({
        content: '查询发布成功',
        description: '版本已成功发布并激活',
        duration: 3
      });
      
      // 发布成功，返回成功标识
      return { success: true, message: '发布成功' };
    } catch (error) {
      console.error('发布查询失败:', error);
      
      // 检查是否是用户取消操作
      if (error instanceof Error && error.message === 'USER_CANCELLED') {
        // 用户取消，不显示错误消息，直接抛出异常让调用方处理
        throw error;
      }
      
      message.error({
        content: '发布查询失败',
        description: error instanceof Error ? error.message : '发布操作未能完成',
        duration: 5
      });
      throw error;
    }
  };

  /**
   * 加载查询版本信息
   */
  const loadVersions = async (queryId: string) => {
    if (!queryId) return;

    try {
      // 重置版本相关状态
      availableVersions.value = [];
      let highestVersionNumber = 0;

      // 尝试获取版本列表
      let versionList: QueryVersion[] = [];
      try {
        // 导入版本服务获取版本列表
        const { versionService } = await import('@/services/queryVersion');

        if (versionService && versionService.getVersions) {
          const versionsResponse = await versionService.getVersions({ queryId, page: 1, size: 50 });
          if (versionsResponse && versionsResponse.items) {
            versionList = versionsResponse.items;
            console.log('获取到版本列表:', versionList);

            // 如果有版本，尝试加载第一个版本的详细信息，以确保获取到完整SQL内容
            if (versionList.length > 0) {
              try {
                const latestVersion = versionList[0];
                console.log('尝试获取最新版本的详细信息:', latestVersion.id);
                const versionDetail = await versionService.getVersion(latestVersion.id);
                console.log('版本详细信息:', versionDetail);

                // 将详细版本信息添加到列表中，替换原始条目
                const indexToReplace = versionList.findIndex(v => v.id === latestVersion.id);
                if (indexToReplace >= 0) {
                  versionList[indexToReplace] = versionDetail;
                }

                // 输出SQL内容，判断是否获取成功
                console.log('获取到的SQL内容:', versionDetail.queryText);
                if (!versionDetail.queryText) {
                  console.warn('SQL内容为空，可能未正确获取');
                }
              } catch (detailError) {
                console.error('获取版本详情失败:', detailError);
              }
            }
          }
        }
      } catch (error) {
        console.warn('获取版本列表失败:', error);
        // 如果无法获取版本列表，使用模拟数据
        versionList = [];
      }

      // 如果有版本列表，从中提取版本号
      if (versionList.length > 0) {
        // 为可用版本列表填充数据
        versionList.forEach(version => {
          const vNum = version.versionNumber;
          if (vNum) {
            availableVersions.value.push(`V${vNum}`);
            // 更新最高版本号
            if (vNum > highestVersionNumber) {
              highestVersionNumber = vNum;
            }
          }
        });

        // 确保版本号排序
        sortVersions();
      } else {
        // 如果没有获取到版本，设置默认值
        availableVersions.value = ['V1'];
        highestVersionNumber = 1;
      }

      // 设置当前最高版本号
      currentMaxVersionNumber.value = highestVersionNumber;
      console.log('设置当前最高版本号:', currentMaxVersionNumber.value);

      // 确保可用版本列表有值
      if (availableVersions.value.length === 0) {
        console.log('可用版本列表为空，添加默认版本V1');
        availableVersions.value = ['V1'];
        currentMaxVersionNumber.value = Math.max(currentMaxVersionNumber.value, 1);
      }

      // 设置当前显示的版本号
      selectedVersion.value = `V${currentMaxVersionNumber.value}`;
      queryVersion.value = selectedVersion.value;

      console.log('版本加载完成，当前版本：', queryVersion.value);
    } catch (error) {
      console.error('加载版本信息失败:', error);
      message.error({
        content: '加载版本信息失败',
        description: error instanceof Error ? error.message : String(error),
        duration: 5
      });
    }
  };

  return {
    versionStatus,
    isActiveVersion,
    availableVersions,
    selectedVersion,
    currentMaxVersionNumber,
    queryVersion,
    publishedAt,
    lastEditedAt,
    deprecatedAt,
    createNewVersion,
    publishVersion,
    loadVersions
  };
}
