/**
 * 执行历史服务
 * 提供SQL执行历史相关的API调用功能
 */

import instance from '@/utils/axios';
import { getApiUrl } from './apiUtils';
import type {
  ExecutionHistory,
  ExecutionHistoryQueryParams,
  ExecutionHistoryPaginationResponse
} from '@/types/execution-history';

/**
 * 执行历史服务
 */
export const executionHistoryService = {
  /**
   * 获取执行历史列表
   * @param params 查询参数
   */
  getExecutionHistory: async (params?: ExecutionHistoryQueryParams): Promise<any> => {
    console.log('[执行历史Service] 获取执行历史列表, 参数:', params);
    
    // 构建查询参数
    const queryParams: Record<string, any> = {};
    
    if (params) {
      // 分页参数
      if (params.page !== undefined) queryParams.page = params.page;
      if (params.size !== undefined) queryParams.size = params.size;
      
      // 筛选条件
      if (params.dataSourceName) queryParams.dataSourceName = params.dataSourceName;
      if (params.schemaName) queryParams.schemaName = params.schemaName;
      if (params.executedAtStart) queryParams.executedAtStart = params.executedAtStart;
      if (params.executedAtEnd) queryParams.executedAtEnd = params.executedAtEnd;
      if (params.executedBy) queryParams.executedBy = params.executedBy;
      if (params.status) queryParams.status = params.status;
    }
    
    const response = await instance.get(`${getApiUrl()}/execution-history`, { 
      params: queryParams 
    });
    
    return response;
  },

  /**
   * 获取执行历史详情
   * @param id 执行历史ID
   */
  getExecutionHistoryById: async (id: string): Promise<any> => {
    console.log(`[执行历史Service] 获取执行历史详情 (ID: ${id})`);
    const response = await instance.get(`${getApiUrl()}/execution-history/${id}`);
    return response;
  },

  /**
   * 获取执行结果
   * @param resultId 结果ID
   */
  getExecutionResult: async (resultId: string): Promise<any> => {
    console.log(`[执行历史Service] 获取执行结果 (ID: ${resultId})`);
    const response = await instance.get(`${getApiUrl()}/execution-result/${resultId}`);
    return response;
  },

  /**
   * 重新执行查询
   * @param id 执行历史ID
   */
  reExecuteQuery: async (id: string): Promise<any> => {
    console.log(`[执行历史Service] 重新执行查询 (ID: ${id})`);
    const response = await instance.post(`${getApiUrl()}/execution-history/${id}/re-execute`);
    return response;
  },

  /**
   * 取消执行
   * @param id 执行历史ID
   */
  cancelExecution: async (id: string): Promise<void> => {
    console.log(`[执行历史Service] 取消执行 (ID: ${id})`);
    await instance.post(`${getApiUrl()}/execution-history/${id}/cancel`);
  },

  /**
   * 删除执行历史
   * @param id 执行历史ID
   */
  deleteExecutionHistory: async (id: string): Promise<void> => {
    console.log(`[执行历史Service] 删除执行历史 (ID: ${id})`);
    await instance.delete(`${getApiUrl()}/execution-history/${id}`);
  },

  /**
   * 批量删除执行历史
   * @param ids 执行历史ID数组
   */
  batchDeleteExecutionHistory: async (ids: string[]): Promise<void> => {
    console.log(`[执行历史Service] 批量删除执行历史 (IDs: ${ids.join(', ')})`);
    await instance.delete(`${getApiUrl()}/execution-history/batch`, {
      data: { ids }
    });
  }
};
