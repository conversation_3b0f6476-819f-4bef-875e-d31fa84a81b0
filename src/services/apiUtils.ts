/**
 * API 工具函数
 * 用于处理与后端API通信的通用功能
 */

import type { ApiMappingConfig, ApiResponse, ApiResponseStatus } from '@/types/api-mapping';
import { getApiBaseUrl } from './query';
import defaultApiMapping from '@/config/api-mapping.json';
import instance from '@/utils/axios';

// 扩展默认配置以匹配 ApiMappingConfig 类型
const extendedApiMapping: ApiMappingConfig = {
  ...defaultApiMapping as any,
  baseConfig: {
    ...defaultApiMapping.baseConfig,
    defaultTimeoutMs: defaultApiMapping.baseConfig.connectionTimeout
  },
  metadata: {
    ...defaultApiMapping.metadata,
    table: defaultApiMapping.metadata.tables || '/metadata/tables',
    views: '/metadata/views',
    statistics: '/metadata/statistics',
    relationships: '/metadata/relationships',
    indexes: '/metadata/tables/{tableId}/indexes',
    'indexes-refresh': '/metadata/tables/{tableId}/indexes/refresh'
  }
} as ApiMappingConfig;

// 添加调试信息
console.log('[apiUtils] extendedApiMapping 创建完成，metadata 部分:', extendedApiMapping.metadata);
console.log('[apiUtils] 检查 indexes 键:', extendedApiMapping.metadata.indexes);
console.log('[apiUtils] 检查 indexes-refresh 键:', extendedApiMapping.metadata['indexes-refresh']);

let ApiMapping: ApiMappingConfig = extendedApiMapping;

/**
 * 加载API映射配置
 */
export async function loadApiMapping(): Promise<void> {
  try {
    const response = await instance.get('/api-mapping.json');
    const config = response.data;
    // 确保扩展配置以匹配 ApiMappingConfig 类型
    ApiMapping = {
      ...config,
      baseConfig: {
        ...config.baseConfig,
        defaultTimeoutMs: config.baseConfig.connectionTimeout
      },
      metadata: {
        ...config.metadata,
        table: config.metadata.tables || '/metadata/tables',
        views: '/metadata/views',
        statistics: '/metadata/statistics',
        relationships: '/metadata/relationships',
        indexes: '/metadata/tables/{tableId}/indexes',
        'indexes-refresh': '/metadata/tables/{tableId}/indexes/refresh'
      }
    } as ApiMappingConfig;
    
    // 添加调试信息
    console.log('[apiUtils] loadApiMapping 完成，新的ApiMapping:', ApiMapping);
    console.log('[apiUtils] metadata 部分:', ApiMapping.metadata);
    console.log('[apiUtils] 检查 indexes 键:', ApiMapping.metadata.indexes);
    console.log('[apiUtils] 检查 indexes-refresh 键:', ApiMapping.metadata['indexes-refresh']);
  } catch (error) {
    console.warn('Failed to load API mapping, using default:', error);
    ApiMapping = extendedApiMapping;
  }
}

/**
 * 获取基础API URL
 */
export function getApiUrl(): string {
  const configBaseUrl = ApiMapping.baseConfig.apiBaseUrl || '';
  const baseUrlFromConfig = getApiBaseUrl();

  // 防止API路径前缀重复
  let baseUrl = '';

  // 简化的逻辑：优先使用环境变量中的baseUrl，如果没有则使用配置文件中的值
  if (baseUrlFromConfig) {
    const isDev = import.meta.env.DEV;
    baseUrl = `${baseUrlFromConfig}${ isDev ? '' : '/api' }`;
    console.log('[API] 使用环境变量中的API基础URL:', baseUrl);
  } else {
    baseUrl = configBaseUrl;
    console.log('[API] 使用配置文件中的API基础URL:', baseUrl);
  }

  // 确保baseUrl没有尾部斜杠
  return baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
}

export interface ApiMapping {
  baseConfig: {
    apiBaseUrl: string;
    mockDataEnabled: boolean;
    autoSwitchToMock: boolean;
    connectionTimeout: number;
    defaultTimeoutMs: number;
  };
  datasource: {
    list: string;
    detail: string;
    create: string;
    update: string;
    delete: string;
    test: string;
    metadata: string;
    sync: string;
    schemas: string;
    tables: string;
    search: string;
  };
  metadata: {
    schemas: string;
    tables: string;
    columns: string;
    sync: string;
    indexes: string;
    'indexes-refresh': string;
  };
  query: {
    list: string;
    detail: string;
    create: string;
    update: string;
    delete: string;
    "execute-sql": string;
    "execute-by-id": string;
    "execute-version": string;
    cancel: string;
    history: string;
    search: string;
    "nlq-execute": string;
    status: string;
    parameters: string;
    favorite: string;
    favorites: string;
    "execution-plan": string;
  };
  version: {
    list: string;
    detail: string;
    create: string;
    update: string;
    delete: string;
    publish: string;
    activate: string;
    deprecate: string;
  };
  integration: {
    list: string;
    detail: string;
    create: string;
    update: string;
    delete: string;
    test: string;
    sync: string;
  };
}

/**
 * 获取数据源管理相关的API URL
 */
export function getDataSourceApiUrl(path: string, params?: Record<string, string>): string {
  const baseUrl = getApiUrl();
  const apiPath = ApiMapping.datasource[path as keyof typeof ApiMapping.datasource];
  if (!apiPath) {
    throw new Error(`Invalid data source API path: ${path}`);
  }
  return constructUrl(baseUrl + apiPath, params);
}

/**
 * 获取元数据管理相关的API URL
 */
export function getMetadataApiUrl(path: string, params?: Record<string, string>): string {
  const baseUrl = getApiUrl();
  
  // 添加调试信息
  console.log(`[getMetadataApiUrl] 请求路径: ${path}`);
  console.log(`[getMetadataApiUrl] ApiMapping.metadata 对象:`, ApiMapping.metadata);
  
  const apiPath = ApiMapping.metadata[path as keyof typeof ApiMapping.metadata];
  
  if (!apiPath) {
    console.error(`[getMetadataApiUrl] 无效的元数据API路径: ${path}`);
    console.error(`[getMetadataApiUrl] 可用的路径:`, Object.keys(ApiMapping.metadata));
    throw new Error(`Invalid metadata API path: ${path}`);
  }
  return constructUrl(baseUrl + apiPath, params);
}

/**
 * 获取查询管理相关的API URL
 */
export function getQueryApiUrl(path: string, params?: Record<string, string>): string {
  const baseUrl = getApiUrl();
  const apiPath = ApiMapping.query[path as keyof typeof ApiMapping.query];
  if (!apiPath) {
    throw new Error(`Invalid query API path: ${path}`);
  }
  return constructUrl(baseUrl + apiPath, params);
}

/**
 * 获取版本管理相关的API URL
 */
export function getVersionApiUrl(path: string, params?: Record<string, string>): string {
  const baseUrl = getApiUrl();
  const apiPath = ApiMapping.version[path as keyof typeof ApiMapping.version];
  if (!apiPath) {
    throw new Error(`Invalid version API path: ${path}`);
  }
  return constructUrl(baseUrl + apiPath, params);
}

/**
 * 获取集成管理相关的API URL
 */
export function getIntegrationApiUrl(path: string, params?: Record<string, string>): string {
  const baseUrl = getApiUrl();
  const apiPath = ApiMapping.integration[path as keyof typeof ApiMapping.integration];
  if (!apiPath) {
    throw new Error(`Invalid integration API path: ${path}`);
  }
  return constructUrl(baseUrl + apiPath, params);
}

// 导出所有API URL构建函数
export const apiUtils = {
  getApiUrl,
  getDataSourceApiUrl,
  getMetadataApiUrl,
  getQueryApiUrl,
  getVersionApiUrl,
  getIntegrationApiUrl,
  loadApiMapping
};

export default apiUtils;

/**
 * 检查是否启用模拟数据
 * @returns 是否启用模拟数据
 */
export function isMockEnabled(): boolean {
  const enabled = ApiMapping.baseConfig.mockDataEnabled;
  console.log('isMockEnabled调用 - 当前模拟数据状态:', enabled);
  return false; // 强制返回false，禁用模拟数据
}

/**
 * 检查是否自动切换到模拟数据
 * @returns 是否自动切换到模拟数据
 */
export function isAutoSwitchToMockEnabled(): boolean {
  return ApiMapping.baseConfig.autoSwitchToMock;
}

/**
 * 获取API超时时间
 * @returns API超时时间 (ms)
 */
export function getApiTimeout(): number {
  return ApiMapping.baseConfig.defaultTimeoutMs;
}

function constructUrl(baseUrl: string, params?: Record<string, string>): string {
  if (!baseUrl) {
    console.error('[API Utils] 构建URL错误: baseUrl为空');
    return '';
  }

  let url = baseUrl;

  if (params) {
    // 检查并处理参数值
    Object.entries(params).forEach(([key, value]) => {
      // 确保值不是undefined或null
      if (value === undefined || value === null) {
        console.warn(`[API Utils] 参数"${key}"的值为${value}，这可能导致占位符未被替换`);
        return;
      }

      // 检查值是否为空字符串
      if (value === '') {
        console.warn(`[API Utils] 参数"${key}"的值为空字符串，这可能导致占位符未被完全替换`);
      }

      // 检查值是否包含大括号（可能是未处理的模板）
      if (typeof value === 'string' && (value.includes('{') || value.includes('}'))) {
        console.warn(`[API Utils] 参数"${key}"的值"${value}"包含大括号，可能是未处理的模板`);
      }

      // 进行替换
      const placeholder = `{${key}}`;
      if (url.includes(placeholder)) {
        url = url.replace(placeholder, String(value));
      } else {
        console.warn(`[API Utils] URL "${baseUrl}" 中不包含参数"${key}"的占位符`);
      }
    });
  }

  // 检查是否存在未替换的占位符
  const unreplacedParams = url.match(/\{([a-zA-Z0-9_]+)\}/g);
  if (unreplacedParams && unreplacedParams.length > 0) {
    console.warn(`[API Utils] URL构建警告: URL "${url}" 包含未替换的占位符:`, unreplacedParams);
    console.warn(`[API Utils] 提供的参数:`, params);

    // 记录更详细的信息帮助调试
    unreplacedParams.forEach(param => {
      const paramName = param.slice(1, -1); // 移除大括号
      console.warn(`[API Utils] 缺少参数 "${paramName}" 或值为空`);
    });
  }

  return url;
}