/**
 * 版本管理服务
 * 提供查询版本相关的API调用
 */
import axios from 'axios';
import instance from '@/utils/axios';
import type { QueryVersion, QueryVersionStatus, GetVersionsParams, CreateVersionParams } from '@/types/queryVersion';
import { mockQueries } from '@/mock/data/query';
import type { PageResponse } from '@/types/query';
import { getQueryApiUrl } from '@/services/apiUtils';

// 模拟版本数据，用于mock模式
const mockVersions: QueryVersion[] = [];

// 检查是否启用mock模式
const USE_MOCK = import.meta.env.VITE_USE_MOCK_API === 'true';
console.log('版本服务 - Mock模式:', USE_MOCK ? '已启用' : '已禁用');

// 创建一个专用于版本管理的axios实例，指向后端API
const versionApi = axios.create({
  baseURL: import.meta.env.PROD
    ? '/api' // 生产环境使用相对路径，通过代理访问
    : (USE_MOCK ? '' : 'http://localhost:3100/api'), // 开发环境直接访问后端，mock模式下使用空baseURL
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 开发调试信息
console.debug('版本管理服务初始化，baseURL:', versionApi.defaults.baseURL);

// 生成模拟版本数据
function generateMockVersions(queryId: string): QueryVersion[] {
  // 从mockQueries中查找对应的查询
  const query = mockQueries.find(q => q.id === queryId);

  if (!query) {
    console.warn(`未找到ID为${queryId}的查询，返回空版本列表`);
    return [];
  }

  // 如果查询中已有versions字段，使用该字段
  if (query.versions && Array.isArray(query.versions) && query.versions.length > 0) {
    // 转换为QueryVersion格式
    return query.versions.map((v, index) => ({
      id: v.id || `ver-${queryId}-${index + 1}`,
      queryId: queryId,
      versionNumber: v.versionNumber || index + 1,
      queryText: v.sql || query.queryText || '',
      status: v.status as any || 'PUBLISHED',
      isActive: index === 0, // 第一个版本是活跃的
      createdAt: v.createdAt || new Date(Date.now() - index * 86400000).toISOString(),
      updatedAt: v.createdAt || new Date(Date.now() - index * 86400000).toISOString(),
      dataSourceId: v.dataSourceId || query.dataSourceId
    }));
  }

  // 创建3个模拟版本记录
  return Array.from({ length: 3 }, (_, i) => {
    const versionNumber = 3 - i; // 最新的版本在前面
    return {
      id: `ver-${queryId}-${versionNumber}`,
      queryId: queryId,
      versionNumber: versionNumber,
      queryText: query.queryText || `SELECT * FROM example_table WHERE id > ${i} LIMIT 10`,
      status: i === 0 ? 'PUBLISHED' : (i === 1 ? 'DRAFT' : 'DEPRECATED'),
      isActive: i === 0, // 第一个版本是活跃的
      createdAt: new Date(Date.now() - i * 86400000).toISOString(),
      updatedAt: new Date(Date.now() - i * 86400000).toISOString(),
      dataSourceId: query.dataSourceId
    };
  });
}

// 版本管理服务
export const versionService = {
  /**
   * 获取查询的所有版本
   * @param params 查询参数，包含queryId、page、size等
   * @returns 分页版本列表
   */
  getVersions: async (params: GetVersionsParams): Promise<PageResponse<QueryVersion>> => {
    try {
      const { queryId, page = 1, size = 10, status } = params;

      // 检查是否使用模拟数据
      if (USE_MOCK) {
        console.log('使用模拟数据返回查询版本列表');
        // 生成模拟版本数据并保存到 mockVersions 中
        const versions = generateMockVersions(queryId);

        // 更新 mockVersions 数组，添加当前查询的版本
        // 先移除该查询所有旧版本
        const otherVersions = mockVersions.filter((v: QueryVersion) => v.queryId !== queryId);
        // 然后合并其他查询的版本与当前查询的新版本
        mockVersions.length = 0;
        mockVersions.push(...otherVersions, ...versions);

        // 按状态过滤版本
        let filteredVersions = [...versions];
        if (status) {
          filteredVersions = filteredVersions.filter(v => v.status === status);
        }

        // 应用分页
        const total = filteredVersions.length;
        const startIndex = (page - 1) * size;
        const endIndex = Math.min(startIndex + size, total);
        const pagedVersions = filteredVersions.slice(startIndex, endIndex);

        // 返回分页结果
        return {
          items: pagedVersions,
          total: total,
          page: page,
          size: size,
          totalPages: Math.ceil(total / size)
        };
      }

      // 构建URL查询参数
      const queryParams = new URLSearchParams();
      if (page) queryParams.append('page', page.toString());
      if (size) queryParams.append('size', size.toString());
      if (status) queryParams.append('status', status);

      // 使用getQueryApiUrl获取正确的API路径
      const apiUrl = getQueryApiUrl('versions', { id: queryId });
      const url = `${apiUrl}?${queryParams.toString()}&_t=${Date.now()}`;
      console.log(`请求版本列表: ${url}`);

      // 使用instance.get代替versionApi.get
      const response = await instance.get(url);

      console.log('版本列表响应:', response.data);

      // 解析响应数据
      if (response.data) {
        // 尝试提取版本数据和分页信息
        let items: QueryVersion[] = [];
        let total = 0;
        let totalPages = 1;

        // 处理不同的响应格式
        if (Array.isArray(response.data)) {
          // 直接是数组的情况
          items = response.data.map(mapVersionResponse);
          total = items.length;
        } else if (response.data.data) {
          // data字段中有数据
          if (Array.isArray(response.data.data)) {
            items = response.data.data.map(mapVersionResponse);
            total = response.data.total || items.length;
          } else if (response.data.data.items && Array.isArray(response.data.data.items)) {
            // 包含分页信息的格式
            items = response.data.data.items.map(mapVersionResponse);
            total = response.data.data.total || items.length;
            totalPages = response.data.data.totalPages || Math.ceil(total / size);
          }
        } else if (response.data.items && Array.isArray(response.data.items)) {
          // 直接包含items字段
          items = response.data.items.map(mapVersionResponse);
          total = response.data.total || items.length;
          totalPages = response.data.totalPages || Math.ceil(total / size);
        }

        return {
          items,
          total,
          page,
          size,
          totalPages
        };
      }

      throw new Error(response.data?.error?.message || '获取版本数据失败');
    } catch (error: any) {
      console.error('获取查询版本失败:', error);
      // 返回空结果
      return {
        items: [],
        total: 0,
        page: params.page || 1,
        size: params.size || 10,
        totalPages: 0
      };
    }
  },

  /**
   * 获取指定版本详情
   * @param versionId 版本ID
   * @returns 版本详情
   */
  getVersionById: async (versionId: string): Promise<QueryVersion> => {
    try {
      // 检查是否使用模拟数据
      if (USE_MOCK) {
        console.log('使用模拟数据返回版本详情');

        // 从版本ID中提取查询ID（假设格式为 ver-[queryId]-[number]）
        const parts = versionId.split('-');
        if (parts.length >= 3) {
          const queryId = parts[1];
          const versionNumber = parts[2];

          // 获取该查询的所有模拟版本
          const versions = generateMockVersions(queryId);
          // 找到指定版本号的版本
          const version = versions.find(v => v.versionNumber.toString() === versionNumber);

          if (version) {
            return version;
          }
        }

        // 或者直接在mockVersions中查找
        const mockVersion = mockVersions.find(v => v.id === versionId);
        if (mockVersion) {
          return mockVersion;
        }

        // 如果找不到，则创建一个默认的版本对象
        return {
          id: versionId,
          queryId: 'unknown',
          versionNumber: 1,
          queryText: 'SELECT * FROM example_table LIMIT 10',
          status: 'DRAFT',
          isActive: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          dataSourceId: 'unknown'
        };
      }

      // 使用正确的API路径格式 - 使用versionId直接获取
      const url = getQueryApiUrl('version-detail', { id: versionId }) + `?_t=${Date.now()}`;
      console.log(`请求版本详情: ${url}`);

      // 使用instance.get代替versionApi.get
      const response = await instance.get(url);

      console.log('版本详情响应:', response.data);

      if (response.data) {
        // 处理不同的响应格式
        if (response.data.data) {
          return mapVersionResponse(response.data.data);
        } else {
          return mapVersionResponse(response.data);
        }
      }

      throw new Error(response.data?.error?.message || '获取版本详情失败');
    } catch (error: any) {
      console.error('获取版本详情失败:', error);
      throw error;
    }
  },

  /**
   * 创建新版本
   * @param params 创建参数
   * @returns 新版本信息
   */
  createVersion: async (params: CreateVersionParams): Promise<QueryVersion> => {
    try {
      const { queryId, queryText, status = 'DRAFT', dataSourceId } = params;

      // 检查是否使用模拟数据
      if (USE_MOCK) {
        console.log('使用模拟数据创建新版本');

        // 获取查询所有版本
        const queryVersions = mockVersions.filter(v => v.queryId === queryId);

        // 计算下一个版本号
        const nextVersionNumber = queryVersions.length > 0
          ? Math.max(...queryVersions.map(v => v.versionNumber)) + 1
          : 1;

        const newVersion: QueryVersion = {
          id: `ver-${queryId}-${nextVersionNumber}`,
          queryId: queryId,
          versionNumber: nextVersionNumber,
          queryText: queryText,
          status: status as QueryVersionStatus,
          isActive: false, // 默认不激活
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          dataSourceId: dataSourceId
        };

        // 将新版本添加到mock版本列表
        mockVersions.push(newVersion);

        return newVersion;
      }

      // 使用正确的API路径格式创建版本
      const url = getQueryApiUrl('create-version', { id: queryId });
      console.log(`创建版本请求: ${url}`);

      // 准备请求体
      const requestBody = {
        queryText,
        status,
        dataSourceId
      };

      // 使用instance.post代替versionApi.post
      const response = await instance.post(url, requestBody);

      console.log('创建版本响应:', response.data);

      if (response.data) {
        // 处理不同的响应格式
        if (response.data.data) {
          return mapVersionResponse(response.data.data);
        } else {
          return mapVersionResponse(response.data);
        }
      }

      throw new Error(response.data?.error?.message || '创建版本失败');
    } catch (error: any) {
      console.error('创建版本失败:', error);
      throw error;
    }
  },

  /**
   * 更新版本
   * @param versionId 版本ID
   * @param params 更新参数
   * @returns 更新后的版本信息
   */
  updateVersion: async (versionId: string, params: Partial<QueryVersion>): Promise<QueryVersion> => {
    try {
      // 检查是否使用模拟数据
      if (USE_MOCK) {
        console.log('使用模拟数据更新版本');

        // 查找要更新的版本
        const index = mockVersions.findIndex(v => v.id === versionId);

        if (index === -1) {
          throw new Error(`未找到ID为${versionId}的版本`);
        }

        // 更新版本
        mockVersions[index] = {
          ...mockVersions[index],
          ...params,
          updatedAt: new Date().toISOString()
        };

        return mockVersions[index];
      }

      // 使用正确的API路径格式更新版本
      const url = getQueryApiUrl('update-version', { id: versionId });
      console.log(`更新版本请求: ${url}`);

      // 使用instance.put代替versionApi.put
      const response = await instance.put(url, params);

      console.log('更新版本响应:', response.data);

      if (response.data) {
        // 处理不同的响应格式
        if (response.data.data) {
          return mapVersionResponse(response.data.data);
        } else {
          return mapVersionResponse(response.data);
        }
      }

      throw new Error(response.data?.error?.message || '更新版本失败');
    } catch (error: any) {
      console.error('更新版本失败:', error);
      throw error;
    }
  },

  /**
   * 激活指定版本
   * @param versionId 版本ID
   * @returns 是否激活成功
   */
  activateVersion: async (queryId: string, versionId: string): Promise<boolean> => {
    try {
      console.log(`准备激活版本 ID: ${versionId}`);

      // 检查是否使用模拟数据
      if (USE_MOCK) {
        console.log('使用模拟数据激活版本', versionId);

        // 查找版本
        const version = mockVersions.find(v => v.id === versionId);

        if (!version) {
          throw new Error(`未找到ID为${versionId}的版本`);
        }

        // 先取消所有版本的激活状态
        for (const v of mockVersions) {
          if (v.queryId === version.queryId) {
            v.isActive = false;
          }
        }

        // 激活当前版本
        version.isActive = true;
        version.updatedAt = new Date().toISOString();

        return true;
      }

      // 使用正确的API路径格式激活版本
      const url = getQueryApiUrl('activate-version', { id: queryId, versionId });
      console.log(`激活版本请求: ${url}`);

      // 使用instance.post代替versionApi.post
      const response = await instance.post(url);

      console.log('激活版本响应:', response.data);

      // 解析响应
      if (response) {
        if (response.success === true) {
          return true;
        } else if (response.data && response.data.success) {
          return true;
        }
      }

      return false;
    } catch (error: any) {
      console.error('激活版本失败:', error);
      throw error;
    }
  },

  /**
   * 发布版本
   * @param versionId 版本ID
   * @returns 是否发布成功
   */
  publishVersion: async (versionId: string): Promise<boolean> => {
    try {
      // 检查是否使用模拟数据
      if (USE_MOCK) {
        console.log('使用模拟数据发布版本', versionId);

        // 查找版本
        const version = mockVersions.find(v => v.id === versionId);

        if (!version) {
          throw new Error(`未找到ID为${versionId}的版本`);
        }

        // 更新版本状态
        version.status = 'PUBLISHED';
        version.updatedAt = new Date().toISOString();

        return true;
      }

      // 使用正确的API路径格式发布版本
      const url = getQueryApiUrl('publish-version', { id: versionId });
      console.log(`发布版本请求: ${url}`);

      // 使用instance.post代替versionApi.post
      const response = await instance.post(url);

      console.log('发布版本响应:', response);

      // 解析响应
      if (response) {
        if (response.success === true) {
          return true;
        } else if (response.data && response.data.success) {
          return true;
        }
      }

      return false;
    } catch (error: any) {
      console.error('发布版本失败:', error);
      throw error;
    }
  },

  /**
   * 发布前检查 - 检查查询是否已在yop网关暴露（用于发布现有版本）
   * @param queryId 查询ID
   * @param versionId 版本ID
   * @returns 是否已在yop网关暴露
   */
  checkPublishBeforehand: async (queryId: string, versionId: string): Promise<boolean> => {
    try {
      // 检查是否使用模拟数据
      if (USE_MOCK) {
        console.log('使用模拟数据检查发布前置条件', { queryId, versionId });
        // 模拟返回false，表示未在yop网关暴露
        return false;
      }

      // 调用前置接口
      const url = `/api/external/publish-check/${queryId}/${versionId}`;
      console.log(`发布前置检查请求: ${url}`);

      const response = await instance.get(url);
      console.log('发布前置检查响应:', response);

      // 解析响应
      console.log('发布前置检查响应详情:', response);
      
      // 根据接口返回格式 {"success":true,"code":200,"message":"操作成功","data":true}
      if (response && response.data) {
        // 检查 response.data 是否已经是处理后的数据（直接是 data 字段的值）
        if (typeof response.data === 'boolean') {
          console.log('response.data 已经是布尔值:', response.data);
          return response.data === true;
        }
        
        // 如果 response.data 是对象，则按原逻辑处理
        if (typeof response.data === 'object') {
          const responseData = response.data;
          console.log('响应数据结构:', responseData);
          
          // 检查响应结构
          if (responseData.success === true && responseData.data !== undefined) {
            console.log('解析到data字段值:', responseData.data);
            return responseData.data === true;
          }
          // 兼容其他可能的响应格式
          else if (responseData.data !== undefined) {
            console.log('兼容格式解析到data字段值:', responseData.data);
            return responseData.data === true;
          }
        }
      }

      console.log('未找到有效的data字段，返回false');
      return false;
    } catch (error: any) {
      console.error('发布前置检查失败:', error);
      // 如果前置接口调用失败，默认返回false，允许继续发布
      return false;
    }
  },

  /**
   * 发布新版本前检查 - 检查查询是否已在yop网关暴露
   * @param queryId 查询ID
   * @param dataSourceId 数据源ID
   * @param schemaId schema ID
   * @param sql 当前要发布的SQL
   * @returns 是否已在yop网关暴露
   */
  checkPublishNewVersionBeforehand: async (
    queryId: string, 
    dataSourceId: string, 
    schemaId: string, 
    sql: string
  ): Promise<boolean> => {
    try {
      // 检查是否使用模拟数据
      if (USE_MOCK) {
        console.log('使用模拟数据检查发布新版本前置条件', { queryId, dataSourceId, schemaId });
        // 模拟返回false，表示未在yop网关暴露
        return false;
      }

      // 调用前置接口 - POST请求
      const url = `/api/external/publish-check/${queryId}`;
      console.log(`发布新版本前置检查请求: ${url}`);

      // 构建请求体
      const requestBody = {
        dataSourceId,
        schemaName: schemaId, // schemaName代表schemaId
        sql
      };
      console.log('发布新版本前置检查请求体:', requestBody);

      const response = await instance.post(url, requestBody);
      console.log('发布新版本前置检查响应:', response);

      // 解析响应
      console.log('发布新版本前置检查响应详情:', response);
      
      // 根据接口返回格式 {"success":true,"code":200,"message":"操作成功","data":true}
      if (response && response.data) {
        // 检查 response.data 是否已经是处理后的数据（直接是 data 字段的值）
        if (typeof response.data === 'boolean') {
          console.log('response.data 已经是布尔值:', response.data);
          return response.data === true;
        }
        
        // 如果 response.data 是对象，则按原逻辑处理
        if (typeof response.data === 'object') {
          const responseData = response.data;
          console.log('响应数据结构:', responseData);
          
          // 检查响应结构
          if (responseData.success === true && responseData.data !== undefined) {
            console.log('解析到data字段值:', responseData.data);
            return responseData.data === true;
          }
          // 兼容其他可能的响应格式
          else if (responseData.data !== undefined) {
            console.log('兼容格式解析到data字段值:', responseData.data);
            return responseData.data === true;
          }
        }
      }

      console.log('未找到有效的data字段，返回false');
      return false;
    } catch (error: any) {
      console.error('发布新版本前置检查失败:', error);
      // 如果前置接口调用失败，默认返回false，允许继续发布
      return false;
    }
  },

  /**
   * 废弃版本
   * @param versionId 版本ID
   * @returns 是否废弃成功
   */
  deprecateVersion: async (versionId: string): Promise<boolean> => {
    try {
      // 检查是否使用模拟数据
      if (USE_MOCK) {
        console.log('使用模拟数据废弃版本', versionId);

        // 查找版本
        const version = mockVersions.find(v => v.id === versionId);

        if (!version) {
          throw new Error(`未找到ID为${versionId}的版本`);
        }

        // 更新版本状态
        version.status = 'DEPRECATED';
        version.updatedAt = new Date().toISOString();

        return true;
      }

      // 使用正确的API路径格式废弃版本
      const url = getQueryApiUrl('deprecate-version', { id: versionId });
      console.log(`废弃版本请求: ${url}`);

      // 使用instance.post代替versionApi.post
      const response = await instance.post(url);

      console.log('废弃版本响应:', response.data);

      // 解析响应
      if (response.data) {
        if (response.data.success === true) {
          return true;
        } else if (response.data.data && response.data.data.success) {
          return true;
        }
      }

      return false;
    } catch (error: any) {
      console.error('废弃版本失败:', error);
      throw error;
    }
  }
};

/**
 * 将API返回的版本数据映射为QueryVersion类型
 * @param data API返回的版本数据
 * @returns 标准格式的QueryVersion对象
 */
function mapVersionResponse(data: any): QueryVersion {
  return {
    id: data.id || '',
    queryId: data.queryId || '',
    versionNumber: data.versionNumber || 0,
    queryText: data.queryText || data.sql || '',
    status: data.status || 'DRAFT',
    isActive: !!data.isActive,
    createdAt: data.createdAt || new Date().toISOString(),
    updatedAt: data.updatedAt || new Date().toISOString(),
    dataSourceId: data.dataSourceId || ''
  };
}

// 导出版本管理服务
export default versionService;
