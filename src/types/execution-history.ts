/**
 * 执行历史相关类型定义
 */

// 执行历史记录
export interface ExecutionHistory {
  id: string;
  queryId: string;
  versionId: string | null;
  executedBy: string | null;
  executedAt: string;
  status: 'success' | 'error' | 'running' | 'cancelled';
  duration: number;
  rowCount: number;
  parameters: string;
  resultId: string;
  dbSchemaName: string;
  executeSql: string;
  logType?: 'QUERY_SERVICE' | 'INTEGRATION_SERVICE';
  error?: string; // 错误原因
}

// 执行历史查询参数
export interface ExecutionHistoryQueryParams {
  // 分页参数
  page?: number;
  size?: number;
  
  // 筛选条件
  dataSourceName?: string;
  schemaName?: string;
  executedAtStart?: string;
  executedAtEnd?: string;
  executedBy?: string;
  status?: string;
}

// 执行历史分页响应
export interface ExecutionHistoryPaginationResponse {
  success: boolean;
  code: number;
  message: string;
  data: {
    items: ExecutionHistory[];
    total: number;
    page: number;
    size: number;
    totalPages: number;
  };
}

// 执行状态枚举
export enum ExecutionStatus {
  SUCCESS = 'success',
  ERROR = 'error',
  RUNNING = 'running',
  CANCELLED = 'cancelled'
}

// 执行状态显示配置
export const EXECUTION_STATUS_CONFIG = {
  [ExecutionStatus.SUCCESS]: {
    label: '成功',
    color: 'green',
    icon: 'check-circle'
  },
  [ExecutionStatus.ERROR]: {
    label: '失败',
    color: 'red',
    icon: 'close-circle'
  },
  [ExecutionStatus.RUNNING]: {
    label: '执行中',
    color: 'blue',
    icon: 'loading'
  },
  [ExecutionStatus.CANCELLED]: {
    label: '已取消',
    color: 'gray',
    icon: 'minus-circle'
  }
} as const;

// 日志来源显示配置
export const LOG_SOURCE_CONFIG = {
  QUERY_SERVICE: {
    label: '查询服务',
    color: 'blue'
  },
  INTEGRATION_SERVICE: {
    label: '集成服务',
    color: 'green'
  }
} as const;
