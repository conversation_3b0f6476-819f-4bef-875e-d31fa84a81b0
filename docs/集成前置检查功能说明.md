# 集成前置检查功能说明

## 功能概述

在系统集成模块中，当用户点击保存按钮时，系统会先调用一个前置检查接口，用于验证该集成是否已在YOP网关暴露，以避免修改对已暴露的集成产生不必要的影响。

## 功能流程

1. **用户点击保存按钮**
2. **系统执行表单验证**
3. **调用前置检查接口** `/api/external/integration-check/{id}`
4. **根据检查结果决定后续操作**
   - 如果返回 `data: true`：显示确认弹窗
   - 如果返回 `data: false`：直接保存
5. **用户确认后执行保存操作**

## 接口规范

### 前置检查接口

- **请求方式**: POST
- **接口路径**: `/api/external/integration-check/{id}`
- **路径参数**: 
  - `id`: 集成ID
- **请求体**:
  ```json
  {
    "dataSourceId": "数据源ID",
    "queryId": "查询ID", 
    "queryParams": ["查询参数数组"],
    "tableConfig": "表格配置对象(可选)"
  }
  ```
- **响应格式**:
  ```json
  {
    "data": true  // true表示已在YOP网关暴露，false表示未暴露
  }
  ```

## 确认弹窗

当前置检查返回 `data: true` 时，系统会显示确认弹窗：

- **标题**: 确认保存
- **内容**: "该集成配置已在yop网关暴露，修改可能会对此产生影响，是否继续保存？"
- **按钮**: 
  - 继续保存（主要按钮）
  - 取消

## 代码实现

### 1. 集成服务层

在 `src/services/integrationService.ts` 中添加了 `checkIntegrationBeforeSave` 方法：

```typescript
checkIntegrationBeforeSave: async (id: string, data: {
  dataSourceId: string;
  queryId: string;
  queryParams: any[];
  tableConfig?: any;
}): Promise<{ data: boolean }> => {
  const url = getIntegrationApiUrl('check-before-save', { id });
  const response = await instance.post(url, data);
  return response.data;
}
```

### 2. 集成存储层

在 `src/stores/integration.ts` 中添加了前置检查方法：

```typescript
const checkIntegrationBeforeSave = async (id: string, data: {
  dataSourceId: string;
  queryId: string;
  queryParams: any[];
  tableConfig?: any;
}): Promise<boolean> => {
  try {
    const result = await integrationService.checkIntegrationBeforeSave(id, data);
    return result.data;
  } catch (err: any) {
    console.error('集成保存前置检查失败', err);
    // 如果前置检查失败，默认允许保存
    return false;
  }
};
```

### 3. 保存管理组件

在 `src/components/integration/save/SaveManager.vue` 中集成了前置检查逻辑：

```typescript
// 执行前置检查
const checkData = {
  dataSourceId: props.integration.dataSourceId,
  queryId: props.integration.queryId,
  queryParams: props.queryParams,
  tableConfig: props.integration.type === 'TABLE' || props.integration.type === 'SIMPLE_TABLE' 
    ? props.integration.tableConfig 
    : undefined
};

const checkResult = await integrationStore.checkIntegrationBeforeSave(props.integration.id, checkData);

// 如果前置检查返回true，说明该集成已在yop网关暴露，需要用户确认
if (checkResult) {
  // 显示确认弹窗
  return new Promise((resolve) => {
    confirmModal.confirm({
      title: '确认保存',
      content: '该集成配置已在yop网关暴露，修改可能会对此产生影响，是否继续保存？',
      okText: '继续保存',
      cancelText: '取消',
      okButtonType: 'primary',
      onOk: async () => {
        // 用户确认继续保存，执行实际的保存逻辑
        await performSave(integrationObj, true);
        resolve(true);
      },
      onCancel: () => {
        // 用户取消保存
        saveLoading.value = false;
        emit('save-completed', false);
        resolve(false);
      }
    });
  });
}
```

## 配置说明

### API映射配置

在 `src/config/api-mapping.json` 中添加了前置检查接口的映射：

```json
{
  "integration": {
    "check-before-save": "/external/integration-check/{id}"
  }
}
```

## 测试

创建了测试文件 `src/__tests__/integration-check.test.ts` 来验证前置检查功能的正确性，包括：

- 接口调用验证
- 错误处理测试
- 数据结构验证

## 注意事项

1. **错误处理**: 如果前置检查接口调用失败，系统会默认允许保存，避免阻塞用户操作
2. **性能考虑**: 前置检查只在更新现有集成时执行，新建集成时不需要检查
3. **用户体验**: 确认弹窗使用友好的提示文案，让用户了解操作的影响
4. **数据完整性**: 前置检查会传递完整的集成配置信息，确保检查的准确性

## 扩展性

该功能设计具有良好的扩展性，未来可以：

- 添加更多的检查规则
- 支持不同类型的警告信息
- 集成更复杂的权限控制逻辑
- 添加检查结果的缓存机制
