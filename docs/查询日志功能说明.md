# 查询日志功能说明

## 功能概述

查询日志功能用于查看和管理SQL查询的执行历史记录，提供了完整的查询执行监控和管理能力。

## 功能特性

### 1. 查询条件筛选
- **数据源选择**: 支持按数据源筛选执行记录
- **Schema筛选**: 支持按数据库Schema筛选
- **执行时间范围**: 支持按执行时间范围筛选
- **执行人筛选**: 支持按执行人筛选
- **执行状态筛选**: 支持按执行状态（成功/失败/执行中/已取消）筛选

### 2. 数据展示
- **执行时间**: 显示具体的执行时间和耗时
- **执行状态**: 用不同颜色的标签显示执行状态
- **数据源信息**: 显示数据源和Schema信息
- **执行人**: 显示执行人信息
- **执行SQL**: 显示完整的SQL语句（支持长文本截断）
- **执行参数**: 显示查询参数（JSON格式）
- **返回行数**: 显示查询返回的数据行数

### 3. 操作功能
- **查看详情**: 查看完整的执行详情信息
- **查看结果**: 查看查询执行结果（仅成功状态）
- **重新执行**: 重新执行查询
- **删除记录**: 删除单条或批量删除执行记录
- **导出结果**: 支持CSV和Excel格式导出

### 4. 分页支持
- 支持分页显示，默认每页20条记录
- 支持自定义每页显示数量（10/20/50/100）
- 支持快速跳转到指定页面

## 技术实现

### 文件结构
```
src/
├── types/
│   └── execution-history.ts          # 类型定义
├── services/
│   └── executionHistoryService.ts    # API服务
├── views/
│   ├── ExecutionHistoryView.vue      # 主页面
│   └── components/
│       ├── ExecutionDetailModal.vue  # 详情弹窗
│       └── ExecutionResultModal.vue  # 结果查看弹窗
└── router/
    └── index.ts                      # 路由配置
```

### API接口
- `GET /data-scope/api/execution-history` - 获取执行历史列表
- `GET /data-scope/api/execution-history/{id}` - 获取执行历史详情
- `GET /data-scope/api/execution-result/{resultId}` - 获取执行结果
- `POST /data-scope/api/execution-history/{id}/re-execute` - 重新执行查询
- `DELETE /data-scope/api/execution-history/{id}` - 删除执行历史
- `DELETE /data-scope/api/execution-history/batch` - 批量删除执行历史

### 路由配置
- 路径: `/execution-history`
- 组件: `ExecutionHistoryView`
- 布局: `DefaultLayout`

### 导航菜单
在顶部导航栏的"查询服务"和"系统集成"之间添加了"查询日志"入口。

## 使用说明

### 1. 访问查询日志
- 点击顶部导航栏的"查询日志"菜单项
- 或直接访问 `/execution-history` 路径

### 2. 筛选查询记录
1. 选择数据源（可选）
2. 选择Schema（可选）
3. 输入执行人（可选）
4. 选择执行时间范围（可选）
5. 选择执行状态（可选）
6. 点击"查询"按钮

### 3. 查看执行详情
1. 点击表格中的"查看详情"按钮
2. 在弹出的详情窗口中查看完整信息
3. 可以复制SQL语句和执行参数
4. 支持重新执行查询

### 4. 查看执行结果
1. 对于执行成功的记录，点击"查看结果"按钮
2. 在弹出的结果窗口中查看查询结果
3. 支持导出CSV和Excel格式

### 5. 管理执行记录
1. 选择要删除的记录（支持多选）
2. 点击"批量删除"按钮
3. 确认删除操作

## 注意事项

1. **权限控制**: 需要确保用户有相应的权限访问执行历史数据
2. **数据量**: 大量执行记录可能影响页面加载性能，建议合理设置分页大小
3. **API依赖**: 功能依赖后端API接口，需要确保接口正常可用
4. **浏览器兼容**: 使用了现代浏览器特性，建议使用Chrome、Firefox等现代浏览器

## 扩展功能

未来可以考虑添加以下功能：
- 执行性能分析图表
- 查询优化建议
- 执行历史统计报表
- 定时清理历史记录
- 执行结果缓存管理
