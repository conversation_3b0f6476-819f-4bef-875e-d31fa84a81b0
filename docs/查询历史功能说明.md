# 查询历史功能说明

## 功能概述

查询历史功能允许用户在查询编辑页面保留之前查询的SQL记录和结果，方便进行对比分析。除了最近一次查询标注为红色外，其他历史查询都标注为绿色。

## 功能特性

### 1. 自动记录查询历史
- **SQL查询记录**: 每次执行SQL查询时自动保存SQL语句和结果
- **自然语言查询记录**: 每次执行自然语言查询时自动保存问题和结果
- **执行信息记录**: 记录执行时间、状态、数据源等详细信息

### 2. 历史记录显示
- **历史查询绿色标识**: 所有历史查询使用绿色边框和背景标识
- **可展开/收起**: 支持展开查看详细信息或收起节省空间
- **结果预览**: 显示查询结果的前10行数据
- **不显示最近查询**: 最近一次查询结果在上方查询结果面板中显示，历史区域只显示之前的查询

### 3. 历史记录管理
- **清除历史**: 支持一键清除所有查询历史记录
- **删除单条**: 支持删除单条查询历史记录
- **数量限制**: 最多保留10条历史记录，超出自动删除最旧的记录

### 4. 详细信息展示
- **SQL语句**: 完整显示SQL查询语句，支持代码高亮
- **查询结果**: 以表格形式展示查询结果
- **执行统计**: 显示执行时间、行数、状态等信息
- **数据源信息**: 显示查询使用的数据源和Schema

## 使用方法

### 1. 查看查询历史
1. 在查询编辑页面执行SQL查询
2. 查询执行完成后，页面下方会自动显示查询历史区域
3. 点击历史记录标题可以展开/收起详细信息

### 2. 管理查询历史
1. **展开/收起所有记录**: 点击右上角的"展开"/"收起"按钮
2. **清除所有历史**: 点击右上角的"清除历史"按钮
3. **删除单条记录**: 在记录详情中点击"删除"按钮

### 3. 对比查询结果
1. 执行多次不同的SQL查询
2. 在查询历史区域查看所有历史记录
3. 展开不同记录的详细信息进行对比
4. 当前查询结果在上方显示，历史查询在下方以绿色标识显示

## 技术实现

### 文件结构
```
src/
├── stores/
│   └── query.ts                    # 查询存储，包含历史记录管理
├── components/query/
│   └── QueryHistoryDisplay.vue     # 查询历史显示组件
└── views/query/
    └── QueryEditorPage.vue         # 查询编辑页面，集成历史显示
```

### 核心类型定义
```typescript
interface QueryHistoryRecord {
  id: string                    // 历史记录ID
  sql: string                   // SQL语句
  result: QueryResult | null    // 查询结果
  executedAt: string            // 执行时间
  executionTime: number         // 执行耗时（毫秒）
  status: QueryStatus           // 执行状态
  dataSourceId: string          // 数据源ID
  schemaId?: string             // Schema ID
  parameters?: Record<string, any> // 查询参数
  isLatest: boolean             // 是否为最新查询
}
```

### 状态管理
- 使用Pinia store管理查询历史记录状态
- 自动保存每次查询的执行结果
- 支持历史记录的增删改查操作

## 样式设计

### 颜色方案
- **历史查询**: 绿色边框和背景 (`border-green-300 bg-green-50`)
- **SQL代码**: 深色背景配绿色文字，类似终端风格
- **当前查询**: 在上方查询结果面板中显示，不在此区域重复显示

### 交互设计
- 悬停效果和过渡动画
- 可点击的展开/收起区域
- 响应式设计，适配不同屏幕尺寸

## 注意事项

1. **性能考虑**: 历史记录限制为最多10条，避免内存占用过大
2. **数据安全**: 历史记录仅保存在前端，刷新页面后会丢失
3. **结果预览**: 仅显示查询结果的前10行，完整结果需要查看原始查询结果面板
4. **兼容性**: 支持SQL查询和自然语言查询两种模式

## 未来扩展

1. **持久化存储**: 将历史记录保存到后端数据库
2. **导出功能**: 支持导出历史记录为文件
3. **搜索过滤**: 支持按SQL内容或时间范围搜索历史记录
4. **收藏功能**: 支持将重要的查询历史标记为收藏
