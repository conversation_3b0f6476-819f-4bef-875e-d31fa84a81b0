# 索引功能改造说明

## 改造概述

根据需求，对索引查看功能进行了以下改造：

1. **接口1**: 查看索引调用接口：`/api/metadata/tables/{id}/indexes` GET请求
2. **接口2**: 刷新索引按钮改成"同步原数据源索引信息"，调用接口地址：`/api/metadata/tables/{id}/indexes/refresh` POST请求

## 改造内容

### 1. API配置更新

在 `src/config/api-mapping.json` 中添加了新的API端点：

```json
"metadata": {
  "indexes": "/metadata/tables/{tableId}/indexes",
  "indexes-refresh": "/metadata/tables/{tableId}/indexes/refresh"
}
```

### 2. API工具函数更新

在 `src/utils/api.ts` 中的 `getMetadataApiUrl` 函数添加了对新端点的支持：

- `indexes`: 获取表的索引信息
- `indexes/refresh`: 刷新表的索引信息

### 3. 数据源服务更新

`src/services/datasource.ts` 中的两个方法已经正确使用了新的API端点：

- `getTableIndexes(tableId)`: 使用 `indexes` 端点获取索引信息
- `refreshTableIndexes(tableId)`: 使用 `indexes/refresh` 端点刷新索引信息

### 4. 组件界面更新

`src/components/datasource/IndexViewModal.vue` 中的按钮文本已更新：

- 原来的"刷新索引"按钮改为"同步原数据源索引信息"
- 相关的提示信息也相应更新

## API接口规范

### 查看索引接口

- **URL**: `GET /api/metadata/tables/{id}/indexes`
- **描述**: 获取指定表的索引信息
- **参数**: 
  - `id`: 表ID（路径参数）
- **响应**: 索引信息列表

### 同步索引接口

- **URL**: `POST /api/metadata/tables/{id}/indexes/refresh`
- **描述**: 同步原数据源的索引信息
- **参数**:
  - `id`: 表ID（路径参数）
- **响应**: 更新后的索引信息列表

## 使用方式

### 1. 查看索引

```typescript
import { dataSourceService } from '@/services/datasource'

// 获取表的索引信息
const indexes = await dataSourceService.getTableIndexes(tableId)
```

### 2. 同步索引

```typescript
import { dataSourceService } from '@/services/datasource'

// 同步原数据源的索引信息
const refreshedIndexes = await dataSourceService.refreshTableIndexes(tableId)
```

### 3. 在组件中使用

```vue
<template>
  <IndexViewModal
    :visible="showIndexModal"
    :table-id="tableId"
    :table-name="tableName"
    @update:visible="showIndexModal = false"
  />
</template>
```

## 测试验证

可以通过以下方式测试改造后的功能：

1. 打开 `src/components/datasource/IndexViewTest.vue` 页面
2. 点击"打开索引查看模态框"按钮
3. 验证索引信息是否正确加载
4. 点击"同步原数据源索引信息"按钮，验证同步功能是否正常

## 注意事项

1. 确保后端API接口已经实现并正确响应
2. 索引信息的类型定义在 `src/types/metadata.ts` 中
3. 所有相关的错误处理和日志记录都已更新
4. 组件支持模拟数据模式，便于开发和测试

## 后续优化建议

1. 可以添加索引同步的进度指示器
2. 考虑添加索引信息的缓存机制
3. 可以添加索引性能分析功能
4. 考虑支持批量同步多个表的索引信息
